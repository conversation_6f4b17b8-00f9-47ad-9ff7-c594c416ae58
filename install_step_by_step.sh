#!/bin/bash

# 分步安装脚本 - 先安装稳定的依赖项
set -e

echo "=============================================="
echo "  🧩 SuperCluster 分步安装 🤖              "
echo "=============================================="
echo

# 检查环境
PYTHON_VERSION=$(python --version 2>&1 | awk '{print $2}')
TORCH_VERSION=$(python -c "import torch; print(torch.__version__)" 2>/dev/null || echo "Not found")
CUDA_VERSION=$(python -c "import torch; print(torch.version.cuda)" 2>/dev/null || echo "Not found")

echo "✓ Python version: $PYTHON_VERSION"
echo "✓ PyTorch version: $TORCH_VERSION"
echo "✓ CUDA version: $CUDA_VERSION"

echo
echo "🔧 Step 1: 修复pip配置"
echo

# 重置pip配置
pip config unset global.index-url 2>/dev/null || true
pip config unset global.trusted-host 2>/dev/null || true
pip config set global.index-url https://pypi.org/simple/
pip config set global.trusted-host pypi.org

echo "✓ Pip配置已重置为标准PyPI"

echo
echo "📦 Step 2: 安装基础稳定依赖项"
echo

pip install --upgrade pip
pip install -r requirements_basic.txt

echo "✅ 基础依赖项安装完成!"

echo
echo "⚠️  接下来需要手动处理的包:"
echo "   - plotly==5.9.0 (可视化)"
echo "   - Jupyter相关包 (jupyterlab, ipywidgets等)"
echo "   - PyTorch Geometric扩展 (需要CUDA支持)"
echo "   - open3d (3D处理)"
echo "   - wandb (实验跟踪)"
echo
echo "建议:"
echo "1. 先测试基础功能是否正常"
echo "2. 根据需要逐个安装问题包"
echo "3. 对于PyG扩展，使用:"
echo "   pip install pyg_lib torch_scatter torch_cluster -f https://data.pyg.org/whl/torch-2.1.2+cu121.html"

echo
echo "🧪 测试基础导入..."
python -c "
try:
    import torch
    import pytorch_lightning as pl
    import hydra
    import pandas as pd
    import numpy as np
    print('✅ 基础包导入成功!')
except ImportError as e:
    print(f'❌ 导入失败: {e}')
"
