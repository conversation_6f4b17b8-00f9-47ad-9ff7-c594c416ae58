#!/bin/bash

# 分步安装脚本 - 先安装稳定的依赖项
set -e

echo "=============================================="
echo "  🧩 SuperCluster 分步安装 🤖              "
echo "=============================================="
echo

# 检查环境
PYTHON_VERSION=$(python --version 2>&1 | awk '{print $2}')
TORCH_VERSION=$(python -c "import torch; print(torch.__version__)" 2>/dev/null || echo "Not found")
CUDA_VERSION=$(python -c "import torch; print(torch.version.cuda)" 2>/dev/null || echo "Not found")

echo "✓ Python version: $PYTHON_VERSION"
echo "✓ PyTorch version: $TORCH_VERSION"
echo "✓ CUDA version: $CUDA_VERSION"

echo
echo "🔧 Step 1: 配置pip使用清华源"
echo

# 配置清华源加速下载
pip config set global.index-url https://pypi.tuna.tsinghua.edu.cn/simple/
pip config set global.trusted-host pypi.tuna.tsinghua.edu.cn

echo "✓ Pip配置为清华源，下载会更快"

echo
echo "📦 Step 2: 安装基础稳定依赖项"
echo

pip install --upgrade pip
pip install -r requirements_basic.txt

echo "✅ 基础依赖项安装完成!"

echo
echo "⚠️  接下来需要手动处理的包:"
echo "   - plotly==5.9.0 (可视化)"
echo "   - Jupyter相关包 (jupyterlab, ipywidgets等)"
echo "   - PyTorch Geometric扩展 (需要CUDA支持)"
echo "   - open3d (3D处理)"
echo "   - wandb (实验跟踪)"
echo
echo "建议:"
echo "1. 先测试基础功能是否正常"
echo "2. 根据需要逐个安装问题包"
echo "3. 对于PyG扩展，使用:"
echo "   pip install pyg_lib torch_scatter torch_cluster -f https://data.pyg.org/whl/torch-2.1.2+cu121.html"

echo
echo "🧪 测试基础导入..."
python -c "
try:
    import torch
    import pytorch_lightning as pl
    import hydra
    import pandas as pd
    import numpy as np
    print('✅ 基础包导入成功!')
except ImportError as e:
    print(f'❌ 导入失败: {e}')
"
