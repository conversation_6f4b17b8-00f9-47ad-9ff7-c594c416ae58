import numpy as np
import os.path as osp
# from collections import namedtuple
from src.datasets import IGNORE_LABEL as IGNORE


########################################################################
#                         Download information                         #
########################################################################


# DALES in LAS format
LAS_UNTAR_NAME = "railway3d_las"
# DALES in PLY format
PLY_UNTAR_NAME = "railway3d_ply"
# DALES in PLY, only version with intensity and instance labels
OBJECTS_UNTAR_NAME = "railway3d"

# ########################################################################
# #                              Data splits                             #
# ########################################################################

# 20250317 通常情况下，默认使用所有数据
TILES_default = {
        'train': [
    'L5-1-M01-001',
    'L5-1-M01-003',
    'L6-1-M01-002',
    'L6-1-M01-003',
    'L7-1-M01-002',
    'L7-1-M01-003',
    'L10-1-M01-001',
    'L10-1-M01-002',
    'L11-1-M01-002',
    'L11-1-M01-003',
    'L12-1-M01-003',
    'L13-1-M01-002',
    'L13-1-M01-003',
    'L14-1-M01-001',
    'L14-1-M01-002',
    'L15-1-M01-001',
    'L15-1-M01-003',
    'L16-1-M01-002',
    'L16-1-M01-003',
    'L17-1-M01-001',
    'L18-1-M01-002',
    'L18-1-M01-003',
    'L19-1-M01-001',
    'L20-1-M01-001'
    ],

        'val': [
    'L6-1-M01-001',
    'L11-1-M01-001',
    'L12-1-M01-001',
    'L14-1-M01-003',
    'L15-1-M01-002',
    'L17-1-M01-002',
    'L17-1-M01-003',
    'L19-1-M01-003'
    ],

        'test': [
    'L5-1-M01-002',
    'L7-1-M01-001',
    'L10-1-M01-003',
    'L12-1-M01-002',
    'L13-1-M01-001',
    'L16-1-M01-001',
    'L18-1-M01-001',
    'L19-1-M01-002'
    ]
}

TILES = { # 20240603 剔除含有交汇型铁轨的数据
        'train': [
    # 'L5-1-M01-001',
    'L5-1-M01-003',
    # 'L6-1-M01-002',
    'L6-1-M01-003',
    'L7-1-M01-002',
    'L7-1-M01-003',
    'L10-1-M01-001',
    'L10-1-M01-002',
    'L11-1-M01-002',
    'L11-1-M01-003',
    # 'L12-1-M01-003',
    'L13-1-M01-002',
    'L13-1-M01-003',
    # 'L14-1-M01-001',
    'L14-1-M01-002',
    'L15-1-M01-001',
    # 'L15-1-M01-003',
    # 'L16-1-M01-002',
    'L16-1-M01-003',
    'L17-1-M01-001',
    'L18-1-M01-002',
    # 'L18-1-M01-003',
    # 'L19-1-M01-001',
    'L20-1-M01-001'
    ],

        'val': [
    # 'L6-1-M01-001',
    'L11-1-M01-001',
    # 'L12-1-M01-001',
    'L14-1-M01-003',
    # 'L15-1-M01-002',
    'L17-1-M01-002',
    'L17-1-M01-003',
    # 'L19-1-M01-003'
    ],

        'test': [
    # 'L5-1-M01-002',
    'L7-1-M01-001',
    'L10-1-M01-003',
    # 'L12-1-M01-002',
    'L13-1-M01-001',
    'L16-1-M01-001',
    'L18-1-M01-001',
    # 'L19-1-M01-002'
    ]
}


INV_OBJECT_LABEL = {
                        0:  'rails',
                        1:  'track bed',
                        2:  'masts',
                        3:  'support devices',
                        4:  'overhead lines',
                        5:  'fences',
                        6:  'poles',
                        7:  'vegetation',
                        8:  'buildings',
                        9:  'ground', 
                        10: 'other'
                      }
RAILWAY3D_NUM_CLASSES = 11
ID2TRAINID = np.asarray([0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10])
CLASS_NAMES = [INV_OBJECT_LABEL[i] for i in range(RAILWAY3D_NUM_CLASSES)] + ['ignored']

CLASS_COLORS = np.asarray([
    [154,107,56]   ,
    [160,112,160]  ,
    [251,143,49]   ,
    [62,170,163]   ,
    [229,202,63]   ,
    [101,101,206]  ,
    [180,47,174]   ,
    [166,255,64]   ,
    [253,51,103]   ,
    [154,194,231]  ,
    [218,219,220]
])

OBJECT_LABEL = {name: i for i, name in INV_OBJECT_LABEL.items()}

def object_name_to_label(object_class):
    """Convert from object name to int label. By default, if an unknown
    object nale
    """
    object_label = OBJECT_LABEL.get(object_class, OBJECT_LABEL["clutter"])
    return object_label

# For instance segmentation
MIN_OBJECT_SIZE = 100

THING_CLASSES = [2, 3, 6]
STUFF_CLASSES = [i for i in range(RAILWAY3D_NUM_CLASSES) if not i in THING_CLASSES]
