import os
import sys
import torch
import shutil
import logging
import h5py
from plyfile import PlyData, PlyElement
from torch_geometric.nn.pool.consecutive import consecutive_cluster
from torch_geometric.data import extract_tar

from src.datasets import BaseDataset
from src.data import Data, InstanceData
from src.datasets.railway3d_config import *
from src.utils.neighbors import knn_2
from src.utils import to_float_rgb
from src.utils.helper_ply import write_ply

DIR = os.path.dirname(os.path.realpath(__file__))
log = logging.getLogger(__name__)

# Occasional Dataloader issues with Railway3D on some machines. Hack to
# solve this:
# https://stackoverflow.com/questions/73125231/pytorch-dataloaders-bad-file-descriptor-and-eof-for-workers0
import torch.multiprocessing
torch.multiprocessing.set_sharing_strategy('file_system')


__all__ = ['Railway3D', 'MiniRailway3D']

########################################################################
#                                 Utils                                #
########################################################################

def read_railway3d_tile(
        filepath, xyz=True, rgb=False, intensity=False, semantic=True, instance=True,
        remap=True):
    """Read a Railway3sd tile saved as PLY.
            :param filepath: str
                Absolute path to the PLY file
            :param xyz: bool
                Whether XYZ coordinates should be saved in the output Data.pos
            :param rgb: bool
                Whether RGB colors should be saved in the output Data.rgb
            :param semantic: bool
                Whether semantic labels should be saved in the output Data.y
            :param instance: bool
                Whether instance labels should be saved in the output Data.obj
            :param remap: bool
                Whether semantic labels should be mapped from their DALES ID
                to their train ID.
    """
    data = Data()
    with open(filepath, "rb") as f:
        key = 'vertex'
        tile = PlyData.read(f)
        # print("_get_elements", tile._get_elements())
        # exit()

        if xyz:
            xyz_data = np.vstack((tile[key]['x'], tile[key]['y'], tile[key]['z'])).T
            xyz_data = xyz_data.astype(np.double)
            data.pos = torch.FloatTensor(xyz_data)
            # print("sucessfully load xyz data")

        if rgb:
            rgb_data = np.vstack((tile[key]['red'], tile[key]['green'], tile[key]['blue'])).T
            data.rgb = to_float_rgb(torch.from_numpy(rgb_data))
            # print("sucessfully load rgb data")

        if intensity:
            intensity = tile[key]['intensity']
            intensity = intensity.reshape(np.size(intensity, 0), 1)
            # print('original_intensity.max = ', intensity.max())
            intensity[intensity > 800] = 800
            intensity = intensity / 800
            # print('new_intensity.max = ', intensity.max())
            intensity = intensity.astype(np.double)
            # intensity = torch.FloatTensor(intensity)
            # data.intensity = torch.from_numpy(intensity)
            data.intensity = torch.FloatTensor(intensity)
            # print("sucessfully load intensity data")

        if semantic:
            labels = tile[key]['class']
            y = torch.LongTensor(labels)
            data.y = torch.from_numpy(ID2TRAINID)[y] if remap else y
            # print("sucessfully load gt label")

        if instance:
            idx = torch.arange(data.num_points)
            obj_instance = np.array(tile[key]['instance'])
            obj_instance = obj_instance.astype(np.int32)
            obj = torch.LongTensor(obj_instance)
            obj = consecutive_cluster(obj)[0]
            count = torch.ones_like(obj)
            labels = tile[key]['class']
            y = torch.LongTensor(labels)
            y = torch.from_numpy(ID2TRAINID)[y] if remap else y
            data.obj = InstanceData(idx, obj, count, y, dense=True)
            # print("sucessfully load instance data")

    return data

########################################################################
#                                Railway3D                             #
########################################################################

class Railway3D(BaseDataset):
    """ Railway3D dataset.
        Dataset website: https://udayton.edu/engineering/research/centers/vision_lab/research/was_data_analysis_and_processing/dale.php

        Parameters
        ----------
        root : `str`
            Root directory where the dataset should be saved.
        stage : {'train', 'val', 'test', 'trainval'}, optional
        transform : `callable`, optional
            transform function operating on data.
        pre_transform : `callable`, optional
            pre_transform function operating on data.
        pre_filter : `callable`, optional
            pre_filter function operating on data.
        on_device_transform: `callable`, optional
            on_device_transform function operating on data, in the
            'on_after_batch_transfer' hook. This is where GPU-based
            augmentations should be, as well as any Transform you do not
            want to run in CPU-based DataLoaders
    """

    _unzip_name = OBJECTS_UNTAR_NAME

    @property
    def class_names(self):
        """List of string names for dataset classes. This list must be
        one-item larger than `self.num_classes`, with the last label
        corresponding to 'void', 'unlabelled', 'ignored' classes,
        indicated as `y=self.num_classes` in the dataset labels.
        """
        return CLASS_NAMES

    @property
    def num_classes(self):
        """Number of classes in the dataset. Must be one-item smaller
        than `self.class_names`, to account for the last class name
        being used for 'void', 'unlabelled', 'ignored' classes,
        indicated as `y=self.num_classes` in the dataset labels.
        """
        return RAILWAY3D_NUM_CLASSES

    @property
    def stuff_classes(self):
        """List of 'stuff' labels for INSTANCE and PANOPTIC
        SEGMENTATION (setting this is NOT REQUIRED FOR SEMANTIC
        SEGMENTATION alone). By definition, 'stuff' labels are labels in
        `[0, self.num_classes-1]` which are not 'thing' labels.

        In instance segmentation, 'stuff' classes are not taken into
        account in performance metrics computation.

        In panoptic segmentation, 'stuff' classes are taken into account
        in performance metrics computation. Besides, each cloud/scene
        can only have at most one instance of each 'stuff' class.

        IMPORTANT:
        By convention, we assume `y ∈ [0, self.num_classes-1]` ARE ALL
        VALID LABELS (i.e. not 'ignored', 'void', 'unknown', etc), while
        `y < 0` AND `y >= self.num_classes` ARE VOID LABELS.
        """
        return STUFF_CLASSES

    @property
    def class_colors(self):
        """Colors for visualization, if not None, must have the same
        length as `self.num_classes`. If None, the visualizer will use
        the label values in the data to generate random colors.
        """
        return CLASS_COLORS

    @property
    def all_base_cloud_ids(self):
        """Dictionary holding lists of paths to the clouds, for each
        stage.

        The following structure is expected:
            `{'train': [...], 'val': [...], 'test': [...]}`
        """
        return TILES

    def download_dataset(self):
        # if not osp.exists(osp.join(self.root, self._unzip_name)):
        #     log.error(
        #         f"Check datapath : {osp.join(self.root, self._unzip_name)}!\n")
        #     sys.exit(1)
        print("Already found dataset! ")

    def read_single_raw_cloud(self, raw_cloud_path):
        """Read a single raw cloud and return a `Data` object, ready to
        be passed to `self.pre_transform`.

        This `Data` object should contain the following attributes:
          - `pos`: point coordinates
          - `y`: OPTIONAL point semantic label
          - `obj`: OPTIONAL `InstanceData` object with instance labels
          - `rgb`: OPTIONAL point color
          - `intensity`: OPTIONAL point LiDAR intensity

        IMPORTANT:
        By convention, we assume `y ∈ [0, self.num_classes-1]` ARE ALL
        VALID LABELS (i.e. not 'ignored', 'void', 'unknown', etc),
        while `y < 0` AND `y >= self.num_classes` ARE VOID LABELS.
        This applies to both `Data.y` and `Data.obj.y`.
        """
        ## railway3d dataset ##
        # return read_railway3d_tile(
        #     raw_cloud_path, rgb=False, intensity=True, semantic=True, instance=False,
        #     remap=False)
        return read_railway3d_tile(
            raw_cloud_path, rgb=False, intensity=True, semantic=True, instance=True,
            remap=True)

    @property
    def raw_file_structure(self):
        return f"""
        {self.root}/
            └── raw/
                └── {{train, test}}/
                    └── {{tile_name}}.ply
            """

    def id_to_relative_raw_path(self, id):
        """Given a cloud id as stored in `self.cloud_ids`, return the
        path (relative to `self.raw_dir`) of the corresponding raw
        cloud.
        """
        if id in self.all_cloud_ids['train']:
            stage = 'train'
        elif id in self.all_cloud_ids['val']:
            stage = 'train'
        elif id in self.all_cloud_ids['test']:
            stage = 'test'
        else:
            raise ValueError(f"Unknown tile id '{id}'")
        return osp.join(stage, self.id_to_base_id(id) + '.ply')

    def processed_to_raw_path(self, processed_path):
        """Return the raw cloud path corresponding to the input
        processed path.
        """
        # Extract useful information from <path>
        stage, hash_dir, cloud_id = \
            osp.splitext(processed_path)[0].split('/')[-3:]

        # Raw 'val' and 'trainval' tiles are all located in the
        # 'raw/train/' directory
        stage = 'train' if stage in ['trainval', 'val'] else stage

        # Remove the tiling in the cloud_id, if any
        base_cloud_id = self.id_to_base_id(cloud_id)

        # Read the raw cloud data
        raw_path = osp.join(self.raw_dir, stage, base_cloud_id + '.ply')
        # raw_path = osp.join(self.raw_dir, base_cloud_id + '.ply') # 20240414
        # print('cloud_id = ', cloud_id)
        # print('base_cloud_id = ', base_cloud_id)
        print('processed_to_raw_path: ', raw_path)
        return raw_path

    def make_submission(self, idx, pred, pos, submission_dir):

        # if self.xy_tiling or self.pc_tiling:
        #     raise NotImplementedError(
        #         f"Submission generation not implemented for tiled KITTI360 "
        #         f"datasets yet...")

        # Make sure the prediction is a 1D tensor
        if pred.dim() != 1:
            raise ValueError(
                f'The submission predictions must be 1D tensors, '
                f'received {type(pred)} of shape {pred.shape} instead.')

        # Initialize the submission directory
        submission_dir = submission_dir or self.submission_dir
        if not osp.exists(submission_dir):
            os.makedirs(submission_dir)

        ### save pred result
        xyz = pos.numpy()
        pred_label = pred.numpy()
        pred_label = pred_label.reshape(np.size(pred_label, 0), 1)
        result_all = np.hstack([xyz, pred_label])
        pred_file_path = osp.join(submission_dir, self.cloud_ids[idx] + '.ply')
        write_ply(pred_file_path, result_all, ['x', 'y', 'z', 'pred'])

        # PartDataPath = '/root/qb/data/hu_s3dis/processed/test/5c7c7500cce90a5317d42d6c53411dbf/'+ self.cloud_ids[idx] + '.h5'
        # print('self.cloud_ids[idx] = ', self.cloud_ids[idx])
        # print('PartDataPath = ', PartDataPath)
        # PartData = h5py.File(PartDataPath,'r')
        # print([key for key in PartData.keys()])



        # # Read the raw point cloud
        # raw_path = osp.join(
        #     self.raw_dir, self.id_to_relative_raw_path(self.cloud_ids[idx]))
        # data_raw = self.read_single_raw_cloud(raw_path)
        # # Search the nearest neighbor of each point and apply the
        # # neighbor's class to the points
        # neighbors = knn_2(pos, data_raw.pos, 1, r_max=1)[0]
        # pred_raw = pred[neighbors]
        # # Map TrainId labels to expected Ids
        # pred_raw = np.asarray(pred_raw)
        # # ################################################################## #
        # # pred_remapped = pred_raw + 1 # railway label start from 1， ignore 0
        # # ################################################################## #
        # pred_raw = pred_raw.astype(np.uint8)
        # pred_raw = pred_raw.reshape(np.size(pred_raw, 0), 1)
        # print("shape of pred_remapped = ", pred_raw.shape)
        # print('saving prediction result ! ')
        # pred_file_path = osp.join(submission_dir, self.cloud_ids[idx] + '.ply')
        # xyz = data_raw.pos
        # xyz = xyz.numpy()
        # rgb = data_raw.rgb
        # rgb = rgb.numpy()
        # gt_label = data_raw.y
        # gt_label = gt_label.numpy()
        # gt_label = gt_label.reshape(np.size(gt_label, 0), 1)
        # result_all = np.hstack([xyz, rgb, gt_label, pred_raw])
        # write_ply(pred_file_path, result_all, ['x', 'y', 'z', 'red', 'green', 'blue','class', 'pred'])


########################################################################
#                              MiniRailway3D                               #
########################################################################

class MiniRailway3D(Railway3D):
    """A mini version of Railway3D with only a few windows for
    experimentation.
    """
    _NUM_MINI = 1

    @property
    def all_cloud_ids(self):
        return {k: v[:self._NUM_MINI] for k, v in super().all_cloud_ids.items()}

    @property
    def data_subdir_name(self):
        return self.__class__.__bases__[0].__name__.lower()

    # We have to include this method, otherwise the parent class skips
    # processing
    def process(self):
        super().process()

    # We have to include this method, otherwise the parent class skips
    # processing
    def download(self):
        super().download()
