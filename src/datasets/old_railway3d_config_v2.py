import numpy as np
import os.path as osp
# from collections import namedtuple
from src.datasets import IGNORE_LABEL as IGNORE


########################################################################
#                         Download information                         #
########################################################################


# DALES in LAS format
LAS_UNTAR_NAME = "railway3d_las"
# DALES in PLY format
PLY_UNTAR_NAME = "railway3d_ply"
# DALES in PLY, only version with intensity and instance labels
OBJECTS_UNTAR_NAME = "railway3d"

# ########################################################################
# #                              Data splits                             #
# ########################################################################


TILES1 = {
    'train': [
# 'L5-1-M01-001',
'L5-1-M01-001',
'L5-1-M01-003',
'L6-1-M01-002',
'L6-1-M01-003',
'L7-1-M01-002',
'L7-1-M01-003',
'L10-1-M01-001',
'L10-1-M01-002',
'L11-1-M01-002',
'L11-1-M01-003',
'L12-1-M01-003',
'L13-1-M01-002',
# 'L13-1-M01-003',
'L13-1-M01-003',
'L14-1-M01-001',
'L14-1-M01-002',
'L15-1-M01-001',
'L15-1-M01-003',
'L16-1-M01-002',
'L16-1-M01-003',
'L17-1-M01-001',
'L18-1-M01-002',
'L18-1-M01-003',
'L19-1-M01-001',
'L20-1-M01-001'
],

    'val': [
'L6-1-M01-001',
'L11-1-M01-001',
'L12-1-M01-001',
'L14-1-M01-003',
'L15-1-M01-002',
'L17-1-M01-002',
'L17-1-M01-003',
'L19-1-M01-003'
],

    'test': [
'L5-1-M01-002',
'L7-1-M01-001',
'L10-1-M01-003',
# 'L12-1-M01-002',
'L12-1-M01-002',
'L13-1-M01-001',
'L16-1-M01-001',
'L18-1-M01-001',
'L19-1-M01-002'
]}

TILES = { # 20240603 剔除含有交汇型铁轨的数据
    'train': [
# 'L5-1-M01-001',
'L5-1-M01-003',
# 'L6-1-M01-002',
'L6-1-M01-003',
'L7-1-M01-002',
'L7-1-M01-003',
'L10-1-M01-001',
'L10-1-M01-002',
'L11-1-M01-002',
'L11-1-M01-003',
# 'L12-1-M01-003',
'L13-1-M01-002',
'L13-1-M01-003',
# 'L14-1-M01-001',
'L14-1-M01-002',
'L15-1-M01-001',
# 'L15-1-M01-003',
# 'L16-1-M01-002',
'L16-1-M01-003',
'L17-1-M01-001',
'L18-1-M01-002',
# 'L18-1-M01-003',
# 'L19-1-M01-001',
'L20-1-M01-001'
],

    'val': [
# 'L6-1-M01-001',
'L11-1-M01-001',
# 'L12-1-M01-001',
'L14-1-M01-003',
# 'L15-1-M01-002',
'L17-1-M01-002',
'L17-1-M01-003',
# 'L19-1-M01-003'
],

    'test': [
# 'L5-1-M01-002',
'L7-1-M01-001',
'L10-1-M01-003',
# 'L12-1-M01-002',
'L13-1-M01-001',
'L16-1-M01-001',
'L18-1-M01-001',
# 'L19-1-M01-002'
]}


{
    # TILES = {
#     'train': [
# '11-001_city',
# '11-003_city',
# '12-001_city',
# '12-003_city',
# '13-001_city',
# # '13-003_city',
# '14-001_city',
# '14-003_city',
# '15-001_city',
# '15-003_city',
# '16-001_city',
# '16-003_city',
# '17-001_city',
# '17-003_city',
# '18-001_city',
# '18-003_city',
# '19-001_city',
# '19-003_city',
# # '5-001_city' , 
# '5-003_city' , 
# '6-001_city' ,
# '6-003_city' ,
# '7-001_city' ,
# '7-003_city' ,
# ],

#     'val': [
# '5-002_city' , 
# '6-002_city' ,
# '7-002_city' ,
# '11-002_city',
# # '12-002_city',
# '13-002_city',
# '14-002_city',
# '15-002_city',
# '16-002_city',
# '17-002_city',
# '18-002_city',
# '19-002_city',
# ],

#     'test': [
# '20-001_city',
# ]}
}



INV_OBJECT_LABEL = {
                        0:  'rails',
                        1:  'track bed',
                        2:  'masts',
                        3:  'support devices',
                        4:  'overhead lines',
                        5:  'fences',
                        6:  'poles',
                        7:  'vegetation',
                        8:  'buildings',
                        9:  'ground', 
                        10: 'other'
                      }
RAILWAY3D_NUM_CLASSES = 11
ID2TRAINID = np.asarray([0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10])
# RAILWAY3D_NUM_CLASSES = 10
# ID2TRAINID = np.asarray([0, 1, 2, 3, 4, 5, 6, 7, 8, 9, IGNORE])
CLASS_NAMES = [INV_OBJECT_LABEL[i] for i in range(RAILWAY3D_NUM_CLASSES)] + ['ignored']


##################################### railway3d dataset ## 
{
# CLASS_COLORS = np.asarray([ #这个还是旧版的！！！
#     [154,107,56]  ,  # 0: 铁轨#9a6b38
#     [160,112,160] ,  # 1: 道床#a070a0
#     [229,202,63]  ,  # 2: 接触网#e5ca3f
#     [101,101,206] ,  # 3: 栅栏#6565ce
#     [166,255,64]  ,  # 4: 植被#a6ff40
#     [180,47,174]  ,  # 5: 杆状物#b42fae
#     [251,143,49]  ,  # 6: 支柱钢架#fb8f31
#     [154,194,231] ,  # 7: 地面#9ac2e7
#     [253,51,103]  ,  # 8: 建筑物#fd3367
#     [62,170,163]  ,  # 9: 支撑臂#3eaaa3
#     [218,219,220]    # 10:其他#dadbdc
#     ])
}

CLASS_COLORS = np.asarray([
    [154,107,56]   ,
    [160,112,160]  ,
    [251,143,49]   ,
    [62,170,163]   ,
    [229,202,63]   ,
    [101,101,206]  ,
    [180,47,174]   ,
    [166,255,64]   ,
    [253,51,103]   ,
    [154,194,231]  ,
    [218,219,220]
])

OBJECT_LABEL = {name: i for i, name in INV_OBJECT_LABEL.items()}

def object_name_to_label(object_class):
    """Convert from object name to int label. By default, if an unknown
    object nale
    """
    object_label = OBJECT_LABEL.get(object_class, OBJECT_LABEL["clutter"])
    return object_label

# For instance segmentation
MIN_OBJECT_SIZE = 100
# THING_CLASSES = [5, 6, 10]
THING_CLASSES = []
STUFF_CLASSES = [i for i in range(RAILWAY3D_NUM_CLASSES) if not i in THING_CLASSES]
