import numpy as np
from collections import defaultdict

def compute_per_point_metrics(pred_sem, gt_sem, pred_ins, gt_ins, thing_classes, num_classes):
    """计算单个点云的中间指标（支持多片段非实例类）"""
    pred_sem = np.array(pred_sem)
    gt_sem = np.array(gt_sem)
    pred_ins = np.array(pred_ins)
    gt_ins = np.array(gt_ins)

    # 语义分割混淆矩阵
    semantic_cm = np.zeros((num_classes, num_classes), dtype=int)
    for ps, gs in zip(pred_sem, gt_sem):
        semantic_cm[gs, ps] += 1

    # 初始化实例类和非实例类指标
    metrics = {
        'semantic_cm': semantic_cm,
        'instance_metrics': defaultdict(lambda: {'tp':0, 'fp':0, 'fn':0, 'iou_sum':0, 'total_gt':0, 'total_pred':0}),
        'stuff_matches': defaultdict(lambda: {'matches': [], 'gt_count':0, 'pred_count':0})
    }

    # ------------------- 处理实例类 -------------------
    for cls in thing_classes:
        gt_cls_mask = (gt_sem == cls)
        gt_ins_cls = gt_ins[gt_cls_mask]
        gt_ids = np.unique(gt_ins_cls[gt_ins_cls != -1])
        
        pred_cls_mask = (pred_sem == cls)
        pred_ins_cls = pred_ins[pred_cls_mask]
        pred_ids = np.unique(pred_ins_cls[pred_ins_cls != -1])
        
        metrics['instance_metrics'][cls]['total_gt'] = len(gt_ids)
        metrics['instance_metrics'][cls]['total_pred'] = len(pred_ids)
        
        matched_preds = set()
        for gt_id in gt_ids:
            gt_mask = (gt_ins == gt_id) & (gt_sem == cls)
            best_iou, best_pred_id = 0.0, -1
            for pred_id in pred_ids:
                if pred_id in matched_preds: continue
                pred_mask = (pred_ins == pred_id) & (pred_sem == cls)
                intersect = np.sum(gt_mask & pred_mask)
                union = np.sum(gt_mask | pred_mask)
                if union == 0: continue
                iou = intersect / union
                if iou > best_iou:
                    best_iou, best_pred_id = iou, pred_id
            if best_iou >= 0.5:
                metrics['instance_metrics'][cls]['tp'] += 1
                metrics['instance_metrics'][cls]['iou_sum'] += best_iou
                matched_preds.add(best_pred_id)
            else:
                metrics['instance_metrics'][cls]['fn'] += 1
        metrics['instance_metrics'][cls]['fp'] = len(pred_ids) - len(matched_preds)

    # ------------------- 处理非实例类 -------------------
    for cls in set(range(num_classes)) - set(thing_classes):
        # 提取真实片段
        gt_mask = (gt_sem == cls)
        gt_ins_cls = gt_ins[gt_mask]
        gt_segments = [s for s in np.unique(gt_ins_cls) if s != -1]
        metrics['stuff_matches'][cls]['gt_count'] = len(gt_segments)
        
        # 提取预测片段
        pred_mask = (pred_sem == cls)
        pred_ins_cls = pred_ins[pred_mask]
        pred_segments = [s for s in np.unique(pred_ins_cls) if s != -1]
        metrics['stuff_matches'][cls]['pred_count'] = len(pred_segments)
        
        # 计算所有片段对的IoU
        matches = []
        for gt_seg in gt_segments:
            gt_seg_mask = (gt_ins == gt_seg) & (gt_sem == cls)
            for pred_seg in pred_segments:
                pred_seg_mask = (pred_ins == pred_seg) & (pred_sem == cls)
                intersect = np.sum(gt_seg_mask & pred_seg_mask)
                union = np.sum(gt_seg_mask | pred_seg_mask)
                if union == 0: continue
                iou = intersect / union
                if iou > 0:  # 只要IoU>0就算匹配
                    matches.append(iou)
        metrics['stuff_matches'][cls]['matches'] = matches
    
    return metrics

def aggregate_metrics(all_metrics):
    """合并中间指标"""
    aggregated = {
        'semantic_cm': np.sum([m['semantic_cm'] for m in all_metrics], axis=0),
        'instance_metrics': defaultdict(lambda: {'tp':0, 'fp':0, 'fn':0, 'iou_sum':0, 'total_gt':0, 'total_pred':0}),
        'stuff_matches': defaultdict(lambda: {'matches': [], 'gt_count':0, 'pred_count':0})
    }
    
    for metric in all_metrics:
        # 合并实例类指标
        for cls, data in metric['instance_metrics'].items():
            for k in ['tp', 'fp', 'fn', 'iou_sum', 'total_gt', 'total_pred']:
                aggregated['instance_metrics'][cls][k] += data[k]
        
        # 合并非实例类指标
        for cls, data in metric['stuff_matches'].items():
            aggregated['stuff_matches'][cls]['matches'].extend(data['matches'])
            aggregated['stuff_matches'][cls]['gt_count'] += data['gt_count']
            aggregated['stuff_matches'][cls]['pred_count'] += data['pred_count']
    
    return aggregated

def compute_final_metrics(aggregated, thing_classes, num_classes):
    """计算最终指标（非实例类使用新公式）"""
    # ------------------- 语义分割 -------------------
    semantic_cm = aggregated['semantic_cm']
    iou_per_class = {}
    for c in range(num_classes):
        tp = semantic_cm[c, c]
        fp = np.sum(semantic_cm[:, c]) - tp
        fn = np.sum(semantic_cm[c, :]) - tp
        denom = tp + fp + fn
        iou_per_class[c] = tp / denom if denom > 0 else 0.0
    mIoU = np.mean(list(iou_per_class.values()))

    # ------------------- 实例类指标 -------------------
    instance_results = {}
    for cls in thing_classes:
        data = aggregated['instance_metrics'].get(cls, {'tp':0, 'fp':0, 'fn':0, 'iou_sum':0})
        tp, fp, fn = data['tp'], data['fp'], data['fn']
        precision = tp / (tp + fp) if (tp + fp) > 0 else 0.0
        recall = tp / (tp + fn) if (tp + fn) > 0 else 0.0
        f1 = 2 * (precision * recall) / (precision + recall) if (precision + recall) > 0 else 0.0
        sq = data['iou_sum'] / tp if tp > 0 else 0.0
        instance_results[cls] = {
            'Precision': precision, 'Recall': recall, 'F1': f1,
            'SQ': sq, 'CoV': data['iou_sum'] / data['total_gt'] if data['total_gt'] > 0 else 0.0
        }

    # ------------------- 全景分割（非实例类新公式）---------------------
    panoptic_results = {}
    # 实例类
    for cls in thing_classes:
        data = instance_results.get(cls, {'F1':0, 'SQ':0})
        panoptic_results[cls] = {
            'RQ': data['F1'], 
            'SQ': data['SQ'], 
            'PQ': data['F1'] * data['SQ']
        }
    
    # 非实例类
    stuff_classes = list(set(range(num_classes)) - set(thing_classes))
    for cls in stuff_classes:
        data = aggregated['stuff_matches'][cls]
        S_c = data['gt_count']
        M_c = len(data['matches'])
        
        # RQ = 匹配片段数 / 真实片段数
        RQ = M_c / S_c if S_c > 0 else 0.0
        
        # SQ = 平均匹配IoU
        SQ = np.mean(data['matches']) if M_c > 0 else 0.0
        
        # PQ = RQ × SQ
        PQ = RQ * SQ
        
        panoptic_results[cls] = {'RQ': RQ, 'SQ': SQ, 'PQ': PQ}

    # ------------------- 总体指标 -------------------
    pq_all = [v['PQ'] for v in panoptic_results.values()]
    return {
        'Semantic': {'IoU': iou_per_class, 'mIoU': mIoU},
        'Instance': instance_results,
        'Panoptic': panoptic_results,
        'Mean_PQ': np.mean(pq_all) if pq_all else 0.0
    }

# 示例测试
if __name__ == '__main__':
    pred_sem = [0, 1, 3, 4, 1, 1]
    gt_sem = [0, 0, 2, 3, 1, 1]
    pred_ins = [10, -1, 3, 4, 5, 5]  # 非实例类1有两个预测片段
    gt_ins = [100, 20, 5, 6, 30, 30] # 非实例类1有一个真实片段
    thing_classes = [3, 4]
    num_classes = 5
    
    metrics = compute_per_point_metrics(pred_sem, gt_sem, pred_ins, gt_ins, thing_classes, num_classes)
    aggregated = aggregate_metrics([metrics])
    final_metrics = compute_final_metrics(aggregated, thing_classes, num_classes)
    # print("Panoptic Results (Class 1):", final_metrics['Panoptic'][1])
    print(final_metrics)
