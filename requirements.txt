# SuperCluster Requirements - Compatible with PyTorch 2.1.2 and CUDA 12.1
# Generated for Docker environment with Python 3.10.13

# Core ML/DL frameworks (PyTorch ecosystem - compatible with existing 2.1.2)
# Note: PyTorch 2.1.2 is already installed, so we skip torch and torchvision
torchmetrics>=0.11.0,<1.0.0

# PyTorch Geometric ecosystem - compatible with PyTorch 2.1.2 and CUDA 12.1
# Using specific versions that work with PyTorch 2.1.2
torch-geometric>=2.3.0,<2.4.0
# PyG extensions will be installed separately due to CUDA compatibility

# Lightning framework
pytorch-lightning>=2.0.0,<3.0.0

# Configuration management
hydra-core>=1.3.0,<2.0.0
hydra-colorlog>=1.2.0,<2.0.0
hydra-submitit-launcher>=1.2.0,<2.0.0
omegaconf>=2.3.0,<3.0.0
pyrootutils>=1.0.0,<2.0.0

# Data processing and I/O
h5py>=3.8.0,<4.0.0
plyfile>=0.7.0,<1.0.0
pandas>=1.5.0,<3.0.0
numpy>=1.21.0,<2.0.0

# Visualization and plotting
matplotlib>=3.6.0,<4.0.0
plotly>=5.9.0,<6.0.0
seaborn>=0.12.0,<1.0.0
colorhash>=1.0.0,<2.0.0

# 3D processing
open3d>=0.16.0,<1.0.0

# Scientific computing
numba>=0.56.0,<1.0.0
scipy>=1.9.0,<2.0.0

# Jupyter and interactive tools
jupyterlab>=3.0.0,<5.0.0
ipywidgets>=7.6.0,<9.0.0
jupyter-dash>=0.4.0,<1.0.0
notebook>=6.0.0,<8.0.0
ipykernel>=6.0.0,<7.0.0
ipyfilechooser>=0.6.0,<1.0.0

# Logging and monitoring
wandb>=0.15.0,<1.0.0
rich>=13.0.0,<14.0.0
torch-tb-profiler>=0.4.0,<1.0.0

# Utilities
gdown>=4.6.0,<5.0.0
tqdm>=4.64.0,<5.0.0

# Development and testing (optional but useful)
pytest>=7.0.0,<8.0.0
pytest-cov>=4.0.0,<5.0.0

# Additional dependencies for point cloud processing
# These will be installed from source as per the install script
# FRNN - Fast Range Nearest Neighbors (installed from source)
# Point Geometric Features (installed from source) 
# Parallel Cut-Pursuit (installed from source)

# Note: The following packages need to be installed separately from source:
# 1. FRNN: git clone https://gitee.com/boqiu1314/FRNN.git
# 2. Point Geometric Features: git clone https://gitee.com/boqiu1314/point_geometric_features.git
# 3. Parallel Cut-Pursuit: git clone https://gitee.com/boqiu1314/parallel-cut-pursuit.git
# 4. Grid Graph: git clone https://gitee.com/boqiu1314/grid-graph.git

# For conda-specific packages that need pip alternatives:
# libstdcxx-ng is conda-specific, but usually not needed in Docker with proper base image
