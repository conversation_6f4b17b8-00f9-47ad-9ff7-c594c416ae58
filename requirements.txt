# SuperCluster Requirements - Compatible with PyTorch 2.1.2 and CUDA 12.1
# Generated for Docker environment with Python 3.10.13

# Core ML/DL frameworks (PyTorch ecosystem - compatible with existing 2.1.2)
# Note: PyTorch 2.1.2 is already installed, so we skip torch and torchvision
torchmetrics==0.11.4

# PyTorch Geometric ecosystem - compatible with PyTorch 2.1.2 and CUDA 12.1
# Using specific versions that work with PyTorch 2.1.2
torch-geometric==2.3.0
pyg-lib==0.3.1+pt21cu121 --find-links https://data.pyg.org/whl/torch-2.1.2+cu121.html
torch-scatter==2.1.2+pt21cu121 --find-links https://data.pyg.org/whl/torch-2.1.2+cu121.html
torch-cluster==1.6.3+pt21cu121 --find-links https://data.pyg.org/whl/torch-2.1.2+cu121.html

# Lightning framework
pytorch-lightning==2.1.4

# Configuration management
hydra-core==1.3.2
hydra-colorlog==1.2.0
hydra-submitit-launcher==1.2.0
omegaconf==2.3.0
pyrootutils==1.0.4

# Data processing and I/O
h5py==3.10.0
plyfile==0.7.4
pandas==2.1.4
numpy==1.24.4

# Visualization and plotting
matplotlib==3.8.2
plotly==5.9.0
seaborn==0.13.0
colorhash==1.2.1

# 3D processing
open3d==0.18.0

# Scientific computing
numba==0.58.1
scipy==1.11.4

# Jupyter and interactive tools
jupyterlab>=3.0.0,<4.0.0
ipywidgets>=7.6.0,<8.0.0
jupyter-dash==0.4.2
notebook>=5.3.0,<7.0.0
ipykernel==6.27.1
ipyfilechooser==0.6.0

# Logging and monitoring
wandb==0.16.1
rich==13.7.0
torch-tb-profiler==0.4.3

# Utilities
gdown==4.7.1
tqdm==4.66.1

# Development and testing (optional but useful)
pytest==7.4.3
pytest-cov==4.1.0

# Additional dependencies for point cloud processing
# These will be installed from source as per the install script
# FRNN - Fast Range Nearest Neighbors (installed from source)
# Point Geometric Features (installed from source) 
# Parallel Cut-Pursuit (installed from source)

# Note: The following packages need to be installed separately from source:
# 1. FRNN: git clone https://gitee.com/boqiu1314/FRNN.git
# 2. Point Geometric Features: git clone https://gitee.com/boqiu1314/point_geometric_features.git
# 3. Parallel Cut-Pursuit: git clone https://gitee.com/boqiu1314/parallel-cut-pursuit.git
# 4. Grid Graph: git clone https://gitee.com/boqiu1314/grid-graph.git

# For conda-specific packages that need pip alternatives:
# libstdcxx-ng is conda-specific, but usually not needed in Docker with proper base image
