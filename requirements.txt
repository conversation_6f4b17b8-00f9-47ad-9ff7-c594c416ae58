# SuperCluster Requirements - Docker compatible (no conda)
# Based on x_scripts/install.sh but using pip only

# Core packages (only specify versions where original script does)
matplotlib
plotly==5.9.0
jupyterlab>=3
ipywidgets>=7.6
jupyter-dash
notebook>=5.3
ipykernel
torchmetrics==0.11.4
torch_geometric==2.3.0
plyfile
h5py
colorhash
seaborn
numba
pytorch-lightning
pyrootutils
hydra-core
hydra-colorlog
hydra-submitit-launcher
rich
torch_tb_profiler
wandb
open3d
gdown
ipyfilechooser

# Additional dependencies found in codebase
pandas
omegaconf
tqdm

# Additional dependencies for point cloud processing
# These will be installed from source as per the install script
# FRNN - Fast Range Nearest Neighbors (installed from source)
# Point Geometric Features (installed from source) 
# Parallel Cut-Pursuit (installed from source)

# Note: The following packages need to be installed separately from source:
# 1. FRNN: git clone https://gitee.com/boqiu1314/FRNN.git
# 2. Point Geometric Features: git clone https://gitee.com/boqiu1314/point_geometric_features.git
# 3. Parallel Cut-Pursuit: git clone https://gitee.com/boqiu1314/parallel-cut-pursuit.git
# 4. Grid Graph: git clone https://gitee.com/boqiu1314/grid-graph.git

# For conda-specific packages that need pip alternatives:
# libstdcxx-ng is conda-specific, but usually not needed in Docker with proper base image
