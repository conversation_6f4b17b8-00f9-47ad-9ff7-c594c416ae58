import os
import sys
sys.path.append('/home/<USER>/qbdata/repos/superpoint_transformer_v2')
import torch
import pyrootutils
pyrootutils.setup_root("/home/<USER>/qbdata/repos/superpoint_transformer_v2", pythonpath=True)

# `pyrootutils.setup_root(...)` is an optional line at the top of each entry file
# that helps to make the environment more robust and convenient
import numpy as np
from omegaconf import OmegaConf
OmegaConf.register_new_resolver("eval", eval)

import hydra
import torch
from src.utils import init_config
from src.transforms import *
from src.utils.widgets import *
from src.data import *
from src.datasets.kitti360_config import *
from src.utils.helper_ply import read_ply, write_ply


# Parse the configs using hydra
cfg = init_config(overrides=[
    f"experiment=panoptic/kitti360",  # 设置实验的任务和具体实验
    f"ckpt_path=/home/<USER>/spt_kitti360/checkpoints/epoch_139.ckpt",  # 设置模型权重的路径
    f"datamodule.load_full_res_idx={True}"  # only when you need full-resolution predictions 
])
mode = 'val' # 'train', 'val', 'test'
tasks = 'panoptic'  # 'semantic', 'instance', 'panoptic'
save_dir = cfg.ckpt_path.split('checkpoints/')[0] + 'predict/'
print('save_dir = ', save_dir)
if not os.path.exists(save_dir):
    os.makedirs(save_dir)


# Instantiate the datamodule
datamodule = hydra.utils.instantiate(cfg.datamodule)
datamodule.prepare_data()
datamodule.setup()

# Pick among train, val, and test datasets. It is important to note that
# the train dataset produces augmented spherical samples of large 
# scenes, while the val and test dataset

# dataset_name = datamodule.dataset_name

stage = 'train' if mode in ['train', 'val'] else mode
print('stage = ', stage)
if mode == 'train':
    dataset = datamodule.train_dataset
elif mode == 'val':
    dataset = datamodule.val_dataset
elif mode == 'test':
    dataset = datamodule.test_dataset
print('dataset = ', dataset)

# Load model with feature saving enabled
model = hydra.utils.instantiate(cfg.model)
model = model._load_from_checkpoint(cfg.ckpt_path)
model = model.eval().to('cuda')

for t in dataset.on_device_transform.transforms:
    if isinstance(t, NAGAddKeysTo):
        t.delete_after = False


for idx in range(len(dataset.all_base_cloud_ids[mode])):
    raw_ply_file_path = dataset.processed_to_raw_path(dataset.processed_paths[idx])
    filename = os.path.basename(raw_ply_file_path).split('.')[0]
    print('filename = ', filename)
    nag = dataset[idx]
    nag = dataset.on_device_transform(nag.cuda())

    raw_data = read_ply(raw_ply_file_path)
    xyz = np.vstack((raw_data['x'], raw_data['y'], raw_data['z'])).T
    gt_label = raw_data['semantic']
    gt_label = gt_label.reshape(-1, 1)
    gt_label = ID2TRAINID[gt_label]
    # print('gt_label range = ', np.unique(gt_label))
    gt_instance = raw_data['instance']

    with torch.no_grad():
        output = model(nag)
    nag[0].semantic_pred = output.voxel_semantic_pred(super_index=nag[0].super_index)
    raw_semseg_y = output.full_res_semantic_pred(
        super_index_level0_to_level1=nag[0].super_index,
        sub_level0_to_raw=nag[0].sub)

    if tasks == 'panoptic':
        vox_y, vox_index, vox_obj_pred = output.voxel_panoptic_pred(super_index=nag[0].super_index)
        nag[0].obj_pred = vox_obj_pred

        raw_pano_y, raw_index, raw_obj_pred = output.full_res_panoptic_pred(
            super_index_level0_to_level1=nag[0].super_index, 
            sub_level0_to_raw=nag[0].sub)
        raw_pano_y = raw_pano_y.cpu().numpy()
        raw_index = raw_index.cpu().numpy()
        # Filter raw_index based on gt_label
        thing_classes = dataset.thing_classes
        raw_index_filtered = np.full_like(raw_index, -1)
        for i in range(len(raw_index)):
            if gt_label[i] in thing_classes:
                raw_index_filtered[i] = raw_index[i]

    # print('raw_pano_y', raw_pano_y)
    # print('raw_pano_y range = ', np.unique(raw_pano_y))
    # print('raw_index', raw_index_filtered)
    # print('raw_index range = ', np.unique(raw_index_filtered))
    # print('raw_obj_pred', raw_obj_pred)
    # print('raw_index_filtered range = ', np.unique(raw_index_filtered))

    raw_semseg_y = raw_semseg_y.cpu().numpy()
    raw_semseg_y = raw_semseg_y.reshape(-1, 1)
    # print('CLASS_COLORS', CLASS_COLORS)
    rgb = CLASS_COLORS[raw_semseg_y.squeeze()]

    # print('gt_label.shape = ', gt_label.shape)
    # print('raw_semseg_y.shape = ', raw_semseg_y.shape)
    # print('raw_obj_pred = ', raw_obj_pred)

    error = np.where(gt_label == raw_semseg_y, 0, 1)

    # Save to PLY file
    save_file_path = os.path.join(save_dir, f'{filename}.ply')
    write_ply(save_file_path, 
                [xyz.astype(np.float64),
                rgb.astype(np.uint8),
                gt_label.astype(np.uint8),
                raw_semseg_y.astype(np.uint8),
                error.astype(np.uint8),
                raw_index_filtered.astype(np.int32),
                gt_instance.astype(np.int32)],
                ['x', 'y', 'z', 'red', 'green', 'blue', 
                'gt_label', 'pred_label', 'error', 
                'obj_id_pred', 'obj_id_gt'])

