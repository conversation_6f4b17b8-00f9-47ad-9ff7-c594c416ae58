"""
Required dependencies:
pip install numpy
pip install open3d      # For point cloud processing
pip install scikit-learn  # For RANSAC and other ML algorithms
pip install opencv-python # For image processing and Hough transform
pip install scipy        # For mathematical operations
pip install tqdm
"""

import os
import numpy as np
import open3d as o3d
from sklearn.decomposition import PCA
from sklearn.cluster import DBSCAN
import cv2
from scipy.spatial import KDTree
from scipy.interpolate import splprep, splev
from typing import Tuple, List, Dict
import logging
from helper_ply import read_ply, write_ply
import argparse
from tqdm import tqdm
import traceback
from sklearn.neighbors import BallTree

# 配置日志输出格式
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class RailTrackExtractor:
    def __init__(self, 
                 grid_cell_size: float = 0.25,
                 voxel_size: float = 0.1,
                 intensity_percentile: float = 20,    # 强度阈值百分位数
                 height_percentile_low: float = 1,    # 高度下限百分位数
                 height_percentile_high: float = 99,  # 高度上限百分位数
                 rail_height_threshold: float = 0.05,
                 rail_segment_min_points: int = 30,   # 降低最小点数要求
                 rail_segment_max_gap: float = 2.0,
                 curve_sampling_step: float = 0.5):
        """
        初始化铁轨提取器
        参数说明:
        - grid_cell_size: 网格大小,用于点云分割
        - voxel_size: 下采样体素大小
        - intensity_percentile: 强度阈值百分位数
        - height_percentile_low: 高度下限百分位数
        - height_percentile_high: 高度上限百分位数
        - rail_height_threshold: 判定为铁轨的高度阈值
        - rail_segment_min_points: 一个铁轨段最少需要包含的点数
        - rail_segment_max_gap: 两个铁轨段之间允许的最大距离
        - curve_sampling_step: 曲线拟合时的采样步长
        """
        self.grid_cell_size = grid_cell_size
        self.voxel_size = voxel_size
        self.intensity_percentile = intensity_percentile
        self.height_percentile_low = height_percentile_low
        self.height_percentile_high = height_percentile_high
        self.rail_height_threshold = rail_height_threshold
        self.rail_segment_min_points = rail_segment_min_points
        self.rail_segment_max_gap = rail_segment_max_gap
        self.curve_sampling_step = curve_sampling_step
        self.RAIL_GAUGE = 1.435  # 标准轨距(米)
        self.GAUGE_TOLERANCE = 0.1  # 轨距允许的误差范围(米)

    def voxel_downsample(self, points: np.ndarray, intensities: np.ndarray) -> Tuple[np.ndarray, np.ndarray]:
        """体素下采样"""
        # 计算体素索引
        voxel_indices = np.floor(points / self.voxel_size).astype(int)
        
        # 创建唯一体素的字典
        voxel_dict = {}
        for i, idx in enumerate(voxel_indices):
            idx_tuple = tuple(idx)
            if idx_tuple in voxel_dict:
                voxel_dict[idx_tuple].append(i)
            else:
                voxel_dict[idx_tuple] = [i]
        
        # 对每个体素取平均点
        downsampled_points = []
        downsampled_intensities = []
        for indices in voxel_dict.values():
            downsampled_points.append(np.mean(points[indices], axis=0))
            downsampled_intensities.append(np.mean(intensities[indices]))
        
        return np.array(downsampled_points), np.array(downsampled_intensities)

    def process_directory(self, directory_path: str, output_dir: str, num_files: str = 'all') -> None:
        """
        处理指定目录下的PLY文件
        参数:
        - directory_path: 输入PLY文件目录
        - output_dir: 输出结果目录
        - num_files: 处理文件数量('all'表示处理所有文件,数字表示处理指定数量的文件)
        """
        # 创建输出目录
        os.makedirs(output_dir, exist_ok=True)
        
        # 获取所有PLY文件
        ply_files = [f for f in os.listdir(directory_path) if f.endswith('.ply')]
        
        # 确定要处理的文件数量
        if num_files != 'all':
            try:
                num_files = int(num_files)
                ply_files = ply_files[:num_files]
            except ValueError:
                logger.error("num_files参数必须是整数或'all'")
                return
        
        logger.info(f"开始处理,共{len(ply_files)}个文件")
        
        # 使用tqdm创建进度条
        for filename in tqdm(ply_files, desc="处理文件进度"):
            try:
                self._process_single_file(directory_path, output_dir, filename)
            except Exception as e:
                logger.error(f"处理文件 {filename} 时发生错误: {str(e)}")
                logger.error(traceback.format_exc())  # 打印详细错误栈
                continue

    def _process_single_file(self, directory_path: str, output_dir: str, filename: str):
        """处理单个文件的核心逻辑"""
        file_path = os.path.join(directory_path, filename)
        logger.info(f"\n开始处理文件: {filename}")
        
        # 1. 加载点云
        logger.info("步骤1/6: 加载点云数据...")
        data = read_ply(file_path)
        points = np.vstack((data['x'], data['y'], data['z'])).T
        intensities = data['intensity']
        
        # 2. 下采样
        logger.info("步骤2/6: 点云下采样...")
        points_down, intensities_down = self.voxel_downsample(points, intensities)
        logger.info(f"下采样: {len(points)} -> {len(points_down)} 点")
        
        # 3. 提取铁轨
        logger.info("步骤3/6: 提取铁轨...")
        rail_points_down = self.extract_rails(points_down, intensities_down)
        
        if len(rail_points_down) == 0:
            logger.warning("未检测到铁轨!")
            return
        
        # 4. 在原始点云中恢复完整的铁轨点
        logger.info("步骤4/6: 恢复原始分辨率...")
        rail_points = self.recover_full_resolution(points, rail_points_down)
        
        # 5. 创建分类数组
        logger.info("步骤5/6: 生成分类标签...")
        classification = np.full(len(points), 255, dtype=np.uint8)
        rail_indices = self.find_points_indices(points, rail_points)
        classification[rail_indices] = 0
        
        # 6. 保存结果
        logger.info("步骤6/6: 保存处理结果...")
        self._save_results(output_dir, filename, points, data['intensity'], classification)

    def recover_full_resolution(self, original_points: np.ndarray, rail_points_down: np.ndarray) -> np.ndarray:
        """从下采样的铁轨点恢复原始分辨率的铁轨点"""
        if len(rail_points_down) == 0:
            return np.array([])
            
        # 使用BallTree加速近邻搜索
        tree = BallTree(rail_points_down)
        indices = tree.query_radius(original_points, r=self.voxel_size*1.5)
        
        # 获取在铁轨附近的原始点
        rail_mask = np.array([len(idx) > 0 for idx in indices])
        return original_points[rail_mask]

    def _save_results(self, output_dir: str, filename: str, points: np.ndarray, 
                     intensities: np.ndarray, classification: np.ndarray):
        """保存处理结果"""
        # 创建结构化数组
        data = np.zeros(len(points), dtype=[
            ('x', 'f4'),
            ('y', 'f4'),
            ('z', 'f4'),
            ('intensity', 'f4'),
            ('class', 'u1')
        ])
        
        # 填充数据
        data['x'] = points[:, 0]
        data['y'] = points[:, 1]
        data['z'] = points[:, 2]
        data['intensity'] = intensities
        data['class'] = classification
        
        # 保存结果
        output_path = os.path.join(output_dir, f"classified_{filename}")
        write_ply(output_path, data)
        
        # 输出统计信息
        rail_point_count = np.sum(classification == 0)
        logger.info(f"处理完成: {output_path}")
        logger.info(f"统计信息:")
        logger.info(f"- 总点数: {len(points)}")
        logger.info(f"- 识别出的铁轨点数: {rail_point_count}")
        logger.info(f"- 铁轨点占比: {rail_point_count/len(points)*100:.2f}%")

    def extract_rails(self, points: np.ndarray, intensities: np.ndarray) -> np.ndarray:
        """优化后的铁轨提取主流程"""
        try:
            # 1. 地面分类
            track_bed = self.classify_track_bed(points, intensities)
            if len(track_bed) == 0:
                return np.array([])
            
            # 2. 提取候选点
            rail_candidates = self.extract_rail_candidates_batch(track_bed)
            if len(rail_candidates) == 0:
                return np.array([])
            
            # 3. 分段聚类
            rail_segments = self.segment_rail_points(rail_candidates)
            if not rail_segments:
                return np.array([])
            
            # 4. 拟合曲线并合并
            rail_curves = [self.fit_curve_to_segment(segment) for segment in rail_segments]
            rail_curves = self.merge_related_segments(rail_curves)
            
            # 5. 寻找铁轨对
            rail_pairs = self.find_rail_pairs_curved(rail_curves)
            
            # 6. 生长优化
            final_rails = self.grow_and_refine_rails(rail_pairs, points)
            
            return final_rails
            
        except Exception as e:
            logger.error(f"铁轨提取过程出错: {str(e)}")
            logger.error(traceback.format_exc())
            return np.array([])

    def classify_track_bed(self, points: np.ndarray, intensities: np.ndarray) -> np.ndarray:
        """改进的轨道床分类"""
        # 1. 高度过滤
        z_min = np.percentile(points[:, 2], self.height_percentile_low)
        z_max = np.percentile(points[:, 2], self.height_percentile_high)
        height_mask = (points[:, 2] > z_min) & (points[:, 2] < z_max)
        
        # 2. 强度过滤
        intensity_threshold = np.percentile(intensities, self.intensity_percentile)
        intensity_mask = intensities > intensity_threshold
        
        # 3. 组合过滤器
        mask = height_mask & intensity_mask
        
        filtered_points = points[mask]
        
        # 输出调试信息
        logger.info(f"高度范围: {z_min:.2f} to {z_max:.2f}")
        logger.info(f"强度阈值: {intensity_threshold:.2f}")
        logger.info(f"轨道床点数: {len(filtered_points)} / {len(points)}")
        
        return filtered_points

    def extract_rail_candidates_batch(self, track_bed: np.ndarray) -> np.ndarray:
        """改进的铁轨候选点提取"""
        if len(track_bed) == 0:
            return np.array([])
            
        # 1. 计算局部高度
        tree = BallTree(track_bed)
        radius = 0.3  # 增大搜索半径以获取更好的局部特征
        indices = tree.query_radius(track_bed, radius)
        
        rail_points = []
        local_height_threshold = 0.08  # 增加高度阈值，铁轨通常比周围高8cm以上
        min_neighbors = 10  # 要求最少邻居点数
        
        for i, idx in enumerate(indices):
            if len(idx) < min_neighbors:  # 跳过邻居点太少的点
                continue
                
            neighbors = track_bed[idx]
            center_point = track_bed[i]
            
            # 计算局部高度差
            local_z_min = np.percentile(neighbors[:, 2], 20)  # 使用20%分位数而不是最小值，避免异常值
            height_diff = center_point[2] - local_z_min
            
            if height_diff > local_height_threshold:
                try:
                    # 计算局部PCA
                    pca = PCA(n_components=3)
                    centered_points = neighbors - center_point
                    pca.fit(centered_points)
                    
                    # 检查特征值比例和方向
                    eigenvalues = pca.explained_variance_
                    if (eigenvalues[0] / (eigenvalues[1] + 1e-10) > 3 and  # 提高线性度要求
                        eigenvalues[1] / (eigenvalues[2] + 1e-10) < 2):    # 确保不是平面
                        
                        # 检查主方向是否接近水平
                        principal_direction = pca.components_[0]
                        if abs(principal_direction[2]) < 0.1:  # 主方向与水平面夹角小于约6度
                            rail_points.append(center_point)
                except:
                    continue
        
        rail_points = np.array(rail_points)
        logger.info(f"提取的候选点数: {len(rail_points)}")
        
        if len(rail_points) > 0:
            self.save_debug_cloud("rail_candidates.ply", rail_points)
            
        return rail_points

    def save_debug_cloud(self, filename: str, points: np.ndarray):
        """保存调试用的点云"""
        if len(points) == 0:
            return
        
        # 确保points是numpy数组
        points = np.asarray(points)
        
        # 创建字段数据
        data = np.zeros(len(points), dtype=[
            ('x', 'f4'),
            ('y', 'f4'),
            ('z', 'f4')
        ])
        
        # 填充数据
        data['x'] = points[:, 0]
        data['y'] = points[:, 1]
        data['z'] = points[:, 2]
        
        # 写入PLY文件
        write_ply(filename, data, ['x', 'y', 'z'])

    def segment_rail_points(self, points: np.ndarray) -> List[np.ndarray]:
        """改进的铁轨分段"""
        if len(points) < self.rail_segment_min_points:
            return []
            
        # 调整DBSCAN参数
        eps = 0.3  # 增大聚类距离
        min_samples = 10  # 降低最小样本数
        
        clustering = DBSCAN(eps=eps, min_samples=min_samples).fit(points)
        labels = clustering.labels_
        
        segments = []
        for label in set(labels):
            if label == -1:  # 跳过噪声点
                continue
            segment = points[labels == label]
            if len(segment) >= self.rail_segment_min_points:
                segments.append(segment)
        
        logger.info(f"分割得到的铁轨段数: {len(segments)}")
        return segments

    def fit_curve_to_segment(self, segment: np.ndarray) -> Dict:
        """Fit a smooth curve to a rail segment"""
        # Sort points along principal direction
        pca = PCA(n_components=2)
        points_2d = pca.fit_transform(segment[:, :2])
        
        # Fit spline
        tck, u = splprep([points_2d[:, 0], points_2d[:, 1]], s=0.01, k=3)
        
        # Sample points along the curve
        u_new = np.linspace(0, 1, num=100)
        x_new, y_new = splev(u_new, tck)
        
        # Transform back to original space
        curve_points = pca.inverse_transform(np.column_stack([x_new, y_new]))
        
        return {
            'curve_points': curve_points,
            'spline': tck,
            'pca': pca,
            'original_points': segment
        }

    def merge_related_segments(self, curves: List[Dict]) -> List[Dict]:
        """Merge rail segments that are likely part of the same rail"""
        merged = []
        used = set()
        
        for i in range(len(curves)):
            if i in used:
                continue
                
            current_curve = curves[i]
            merged_segment = current_curve['original_points']
            
            # Look for segments to merge
            for j in range(i + 1, len(curves)):
                if j in used:
                    continue
                    
                other_curve = curves[j]
                
                # Check if segments could be connected
                if self.are_segments_related(current_curve, other_curve):
                    merged_segment = np.vstack([merged_segment, other_curve['original_points']])
                    used.add(j)
            
            # Fit new curve to merged segment
            if len(merged_segment) > len(current_curve['original_points']):
                merged.append(self.fit_curve_to_segment(merged_segment))
            else:
                merged.append(current_curve)
            used.add(i)
            
        return merged

    def are_segments_related(self, curve1: Dict, curve2: Dict) -> bool:
        """Check if two rail segments are likely part of the same rail"""
        # Check end-to-end distance
        dist = np.min([
            np.linalg.norm(curve1['curve_points'][0] - curve2['curve_points'][-1]),
            np.linalg.norm(curve1['curve_points'][-1] - curve2['curve_points'][0])
        ])
        
        if dist > self.rail_segment_max_gap:
            return False
            
        # Check direction continuity
        dir1 = curve1['curve_points'][-1] - curve1['curve_points'][-2]
        dir2 = curve2['curve_points'][1] - curve2['curve_points'][0]
        angle = np.arccos(np.dot(dir1, dir2) / (np.linalg.norm(dir1) * np.linalg.norm(dir2)))
        
        return angle < np.pi/6  # Allow 30 degree difference

    def find_rail_pairs_curved(self, curves: List[Dict]) -> List[Tuple[Dict, Dict]]:
        """Find pairs of parallel curved rails with correct gauge distance"""
        pairs = []
        
        for i in range(len(curves)):
            for j in range(i + 1, len(curves)):
                curve1, curve2 = curves[i], curves[j]
                
                # Check parallel distance along curves
                distances = []
                for k in range(len(curve1['curve_points'])):
                    # Find nearest point on other curve
                    dists = np.linalg.norm(curve2['curve_points'] - curve1['curve_points'][k], axis=1)
                    min_dist = np.min(dists)
                    distances.append(min_dist)
                
                avg_dist = np.mean(distances)
                dist_std = np.std(distances)
                
                # Check if distance matches rail gauge and is consistent
                if (abs(avg_dist - self.RAIL_GAUGE) < self.GAUGE_TOLERANCE and 
                    dist_std < self.GAUGE_TOLERANCE):
                    pairs.append((curve1, curve2))
                    
        return pairs

    def grow_and_refine_rails(self, rail_pairs: List[Tuple[Dict, Dict]], 
                             points: np.ndarray) -> np.ndarray:
        """Grow and refine rail pairs using local point cloud density"""
        tree = KDTree(points)
        all_rail_points = []
        
        for pair in rail_pairs:
            for curve in pair:
                curve_points = curve['curve_points']
                
                # Search for points along the curve
                for i in range(len(curve_points) - 1):
                    # Get local direction
                    direction = curve_points[i + 1] - curve_points[i]
                    direction = direction / np.linalg.norm(direction)
                    
                    # Search for points in cylinder along curve segment
                    center = curve_points[i]
                    indices = tree.query_ball_point(center, r=0.1)
                    
                    if indices:
                        local_points = points[indices]
                        # Filter points by distance to curve and height
                        for point in local_points:
                            dist_to_curve = np.abs(np.cross(direction, point - center))
                            if np.linalg.norm(dist_to_curve) < 0.05:  # 5cm tolerance
                                all_rail_points.append(point)
        
        return np.unique(all_rail_points, axis=0)

    def find_points_indices(self, original_points: np.ndarray, query_points: np.ndarray) -> np.ndarray:
        """Find indices of query points in original point cloud"""
        tree = KDTree(original_points)
        indices = []
        
        for point in query_points:
            # Find exact matches (or very close points)
            dist, idx = tree.query(point, k=1)
            if dist < 1e-6:  # Small threshold for floating point precision
                indices.append(idx)
        
        return np.array(indices)

if __name__ == "__main__":
    # 创建命令行参数解析器
    parser = argparse.ArgumentParser(description='铁轨点云提取工具')
    parser.add_argument('--input_dir', type=str, required=True, help='输入PLY文件目录')
    parser.add_argument('--output_dir', type=str, required=True, help='输出结果目录')
    parser.add_argument('--num_files', type=str, default='all', help="处理文件数量('all'或具体数字)")
    parser.add_argument('--voxel_size', type=float, default=0.1, help="下采样体素大小(米)")
    parser.add_argument('--intensity_percentile', type=float, default=20, help="强度阈值百分位数")
    
    # 解析命令行参数
    args = parser.parse_args()
    
    # 创建提取器实例并处理文件
    extractor = RailTrackExtractor(
        voxel_size=args.voxel_size,
        intensity_percentile=args.intensity_percentile
    )
    extractor.process_directory(
        directory_path=args.input_dir,
        output_dir=args.output_dir,
        num_files=args.num_files
    )
