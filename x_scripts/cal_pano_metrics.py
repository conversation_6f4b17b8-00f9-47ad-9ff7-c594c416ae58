import os
import sys
sys.path.append('/home/<USER>/qbdata/repos/superpoint_transformer_v2')
import pyrootutils
pyrootutils.setup_root("/home/<USER>/qbdata/repos/superpoint_transformer_v2", pythonpath=True)
from src.utils.panoptic_metrics_utils import compute_per_point_metrics, aggregate_metrics, compute_final_metrics
from src.utils.helper_ply import read_ply
import numpy as np
from tqdm import tqdm
from pathlib import Path


def calculate_and_save_metrics(gt_filefold, pred_filefold, thing_classes, num_classes, save_path):
    """
    计算全景分割指标并保存结果
    
    参数:
        gt_filefold: 真值文件夹路径
        pred_filefold: 预测文件夹路径  
        thing_classes: 实例类别列表
        num_classes: 类别总数
        save_path: 结果保存路径
    """
    # 获取文件列表
    gt_file_name_list = [file_name[:-4] for file_name in os.listdir(gt_filefold) if file_name[-4:] == '.ply']
    pred_file_name_list = [file_name[:-4] for file_name in os.listdir(pred_filefold) if file_name[-4:] == '.ply']
    
    assert len(gt_file_name_list) == len(pred_file_name_list), 'gt和pred文件数量必须相同'
    
    print(f"\n开始处理 {len(gt_file_name_list)} 个点云文件...")
    all_metrics = []
    # 使用tqdm创建进度条
    for gt_file_name, pred_file_name in tqdm(zip(gt_file_name_list, pred_file_name_list), 
                                            total=len(gt_file_name_list),
                                            desc="计算指标"):
        gt_file_path = os.path.join(gt_filefold, gt_file_name + '.ply')
        pred_file_path = os.path.join(pred_filefold, pred_file_name + '.ply')
        
        tqdm.write(f"正在处理: {gt_file_name}")
        
        # 读取数据
        gt_data = read_ply(gt_file_path)
        pred_data = read_ply(pred_file_path)
        
        # 计算单个点云的指标
        metrics = compute_per_point_metrics(
            pred_data['pred_label'], 
            gt_data['class'],
            pred_data['instance'],
            gt_data['instance'], 
            thing_classes,
            num_classes
        )
        all_metrics.append(metrics)
    
    print("\n合并所有指标...")
    # 合并所有指标
    aggregated = aggregate_metrics(all_metrics)
    
    print("计算最终指标...")
    # 计算最终指标
    final_metrics = compute_final_metrics(aggregated, thing_classes, num_classes)
    
    print(f"保存结果到: {save_path}")
    # 保存结果到文件
    with open(save_path, 'w') as f:
        # 1. 语义分割指标
        f.write('语义分割指标:\n')
        f.write(f"mIoU: {final_metrics['Semantic']['mIoU']:.4f}\n")
        f.write('每类IoU:\n')
        for cls, iou in final_metrics['Semantic']['IoU'].items():
            f.write(f"类别 {cls}: {iou:.4f}\n")
        f.write('\n')
        
        # 2. 实例分割指标
        f.write('实例分割指标:\n')
        for cls, metrics in final_metrics['Instance'].items():
            f.write(f"类别 {cls}:\n")
            for k, v in metrics.items():
                f.write(f"  {k}: {v:.4f}\n")
        f.write('\n')
        
        # 3. 全景分割指标
        f.write('全景分割指标:\n')
        f.write(f"Mean PQ: {final_metrics['Mean_PQ']:.4f}\n")
        for cls, metrics in final_metrics['Panoptic'].items():
            f.write(f"类别 {cls}:\n")
            for k, v in metrics.items():
                f.write(f"  {k}: {v:.4f}\n")
    
    print("处理完成!")
    return final_metrics

# 使用示例
if __name__ == '__main__':
    # 设置基本参数
    gt_filefold = '/home/<USER>/qbdata/data/railway3d/raw/test'
    panoptic_seg_path = '/home/<USER>/qbdata/data/spt_logs/panoptic_seg'
    metrics_save_dir = '/home/<USER>/qbdata/repos/superpoint_transformer_v2/z_others/panoptic_seg_metrics'
    thing_classes = [2, 3, 6]  # 根据实际情况设置
    num_classes = 11  # 根据实际情况设置

    # 创建保存指标的目录
    Path(metrics_save_dir).mkdir(parents=True, exist_ok=True)
    
    # 获取所有模型目录
    model_dirs = [d for d in os.listdir(panoptic_seg_path) 
                 if os.path.isdir(os.path.join(panoptic_seg_path, d)) and 
                 'panoptic_spilt' in d]
    
    print(f"找到 {len(model_dirs)} 个模型目录:")
    for model_dir in model_dirs:
        print(f"- {model_dir}")
    
    # 依次处理每个模型的预测结果
    for model_dir in model_dirs:
        print(f"\n处理模型: {model_dir}")
        pred_filefold = os.path.join(panoptic_seg_path, model_dir, 'panoptic_predict')
        
        # 检查预测结果目录是否存在
        if not os.path.exists(pred_filefold):
            print(f"警告: 目录不存在 {pred_filefold}")
            continue
            
        # 设置保存路径
        save_path = os.path.join(metrics_save_dir, f'panoptic_metrics_{model_dir}.txt')
        
        try:
            final_metrics = calculate_and_save_metrics(
                gt_filefold,
                pred_filefold,
                thing_classes,
                num_classes,
                save_path
            )
            print(f"成功保存指标到: {save_path}")
        except Exception as e:
            print(f"处理 {model_dir} 时出错: {str(e)}")
            continue 
