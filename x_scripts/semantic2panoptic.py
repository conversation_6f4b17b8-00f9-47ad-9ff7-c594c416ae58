"""
基于语义标签生成实例标签的自动化脚本
适用于铁路场景点云数据（WHU-Railway3D）
"""

import numpy as np
from sklearn.cluster import DBSCAN
from pathlib import Path
import sys

sys.path.append('/home/<USER>/qbdata/repos/superpoint_transformer_v2')

from src.utils.helper_ply import read_ply, write_ply

class InstanceGenerator:
    """实例标签生成器，支持不同类别的差异化聚类参数，并对体积太小的聚类进行过滤。"""

    def __init__(self):
        # 定义需要生成实例的语义类别及对应聚类参数
        self.instance_classes = {
            # 格式：class_id: (eps, min_samples)
            2: (1.5, 10),   # Masts 支柱
            3: (0.8, 5),    # Support devices 支撑装置
            6: (1.0, 3),    # Poles 杆状物
        }
        # 需要保留语义标签但不需要实例化的类别
        self.semantic_only_classes = [0, 1, 4, 5, 7, 8, 9, 10]
        # 定义最小bounding box尺寸阈值，当x, y, z的范围都小于该阈值时，不认为该聚类为有效实例
        self.min_bbox_size = 0.3

    def process_file(self, input_path, output_dir):
        """处理单个PLY文件"""
        # 读取数据并校验字段
        data = read_ply(input_path)
        required_fields = {'x', 'y', 'z', 'class'}
        assert required_fields.issubset(set(data.dtype.names)), "缺少必要字段"

        # 初始化输出数组
        points = np.vstack([data['x'], data['y'], data['z']]).T
        semantic_labels = data['class']
        # 对于非实例类别，统一赋值为-1；后续有实例的点重新赋值
        instance_labels = -1 * np.ones_like(semantic_labels, dtype=np.int32)

        for class_id, (eps, min_samples) in self.instance_classes.items():
            # 提取当前类别的点云
            class_mask = (semantic_labels == class_id)
            class_points = points[class_mask]

            if len(class_points) == 0:
                continue

            # 执行DBSCAN聚类
            db = DBSCAN(eps=eps, min_samples=min_samples).fit(class_points)
            cluster_labels = db.labels_

            # 对每个聚类进行过滤和实例ID赋值
            unique_clusters = np.unique(cluster_labels)
            # 为该类别分配独立ID空间，防止不同类别的实例ID重叠
            unique_base = class_id * 10000
            # 获取当前类点索引
            indices = np.where(class_mask)[0]
            for cl in unique_clusters:
                if cl == -1:
                    continue  # 忽略噪声
                # 找到该聚类在当前类别中的索引
                cluster_idx = np.where(cluster_labels == cl)[0]
                if cluster_idx.size == 0:
                    continue
                pts = class_points[cluster_idx]
                # 计算bounding box范围
                min_vals = pts.min(axis=0)
                max_vals = pts.max(axis=0)
                bbox_size = max_vals - min_vals
                # 如果所有维度的跨度均小于阈值则忽略该聚类
                if np.all(bbox_size < self.min_bbox_size):
                    continue
                # 生成实例ID
                inst_id = unique_base + cl + 1
                # 更新对应全局instance_labels数组
                instance_labels[indices[cluster_idx]] = inst_id

        # 对于不在instance_classes中的类别，instance_labels已经是-1

        # 准备写入数据
        field_list = []
        field_names = []
        
        # 首先添加基本字段
        field_list.extend([
            data['x'],
            data['y'],
            data['z'],
            semantic_labels,
            instance_labels
        ])
        field_names.extend(['x', 'y', 'z', 'class', 'instance'])
        
        # 添加其他可选字段
        optional_fields = ['intensity', 'number_of_returns', 'scan_angle_rank']
        for field in optional_fields:
            if field in data.dtype.names:
                field_list.append(data[field])
                field_names.append(field)

        # 保存结果
        output_path = Path(output_dir) / Path(input_path).name
        write_ply(str(output_path), field_list, field_names)
        print(f"Processed: {input_path} -> {output_path}")


if __name__ == "__main__":
    generator = InstanceGenerator()

    # 定义三个数据场景的文件夹路径
    input_dirs = [
        # "/home/<USER>/qbdata/data/A_railway3d_publish/urban_railways",
        "/home/<USER>/qbdata/data/A_railway3d_publish/rural_railways",
        "/home/<USER>/qbdata/data/A_railway3d_publish/plateau_railways",
    ]
    output_dir = "/home/<USER>/qbdata/data/A_railway3d_publish/panoptic"
    Path(output_dir).mkdir(parents=True, exist_ok=True)

    # 遍历每个文件夹中的所有.ply文件
    for folder in input_dirs:
        folder_path = Path(folder)
        ply_files = list(folder_path.glob("*.ply"))
        print(f"Processing {len(ply_files)} files in {folder}")
        for ply_file in ply_files:
            generator.process_file(str(ply_file), output_dir)
