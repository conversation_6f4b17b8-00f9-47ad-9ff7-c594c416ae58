import os
import sys
sys.path.append('/home/<USER>/code/superpoint_transformer_v2')
import torch
import pyrootutils
pyrootutils.setup_root("/home/<USER>/code/superpoint_transformer_v2", pythonpath=True)

# `pyrootutils.setup_root(...)` is an optional line at the top of each entry file
# that helps to make the environment more robust and convenient
import numpy as np
from omegaconf import OmegaConf
OmegaConf.register_new_resolver("eval", eval)

import hydra
import torch
from src.utils import init_config
from src.transforms import *
from src.utils.widgets import *
from src.data import *
from src.datasets.kitti360_config import *
from src.utils.helper_ply import read_ply, write_ply
from src.utils.neighbors import knn_2

# Define the mapping from the ourKitti360 to wuhan
# ourKitti360:
# 0:  road          
# 1:  sidewalk      
# 2:  building      
# 3:  wall          
# 4:  fence         
# 5:  pole          
# 6:  traffic light 
# 7:  traffic sign  
# 8:  vegetation    
# 9:  terrian       
# 10: car           
# 11: others        

# wuhan:
# 0: road
# 1: sidewalk
# 2: fence
# 3: pole
# 4: traffic light
# 5: traffic sign
# 6: vegetation
# 7: terrian
# 8: others


ourkitti360_to_wuhan = {
    0: 0,  # road
    1: 1,  # sidewalk
    2: 8,  # building -> others
    3: 2,  # wall -> fence
    4: 2,  # fence
    5: 3,  # pole
    6: 4,  # traffic light
    7: 5,  # traffic sign
    8: 6,  # vegetation
    9: 7,  # terrian
    10: 8, # car -> others
    11: 8  # others
}

whu3d_colors = np.asarray([
    [128, 64, 128],
    [244, 35, 232],
    [190, 153, 153],
    [153, 153, 153],
    [250, 170, 30],
    [220, 220, 0],
    [107, 142, 35],
    [152, 251, 152],
    [140, 140, 140],
    [255, 255, 255]
])

# Parse the configs using hydra
cfg = init_config(overrides=[
    f"experiment=panoptic/kitti360",  # 设置实验的任务和具体实验
    f"ckpt_path=/home/<USER>/spt_kitti360/checkpoints/epoch_139.ckpt",  # 设置模型权重的路径
    f"datamodule.load_full_res_idx={True}"  # only when you need full-resolution predictions 
])
# mode = 'val' # 'train', 'val', 'test'
mode = 'test' # 'train', 'val', 'test'
tasks = 'panoptic'  # 'semantic', 'instance', 'panoptic'
save_dir = cfg.ckpt_path.split('checkpoints/')[0] + 'predict_new/'
print('save_dir = ', save_dir)
if not os.path.exists(save_dir):
    os.makedirs(save_dir)


# Instantiate the datamodule
datamodule = hydra.utils.instantiate(cfg.datamodule)
datamodule.prepare_data()
datamodule.setup()

# Pick among train, val, and test datasets. It is important to note that
# the train dataset produces augmented spherical samples of large 
# scenes, while the val and test dataset

# dataset_name = datamodule.dataset_name

stage = 'train' if mode in ['train', 'val'] else mode
print('stage = ', stage)
if mode == 'train':
    dataset = datamodule.train_dataset
elif mode == 'val':
    dataset = datamodule.val_dataset
elif mode == 'test':
    dataset = datamodule.test_dataset
print('dataset = ', dataset)

# Load model with feature saving enabled
model = hydra.utils.instantiate(cfg.model)
model = model._load_from_checkpoint(cfg.ckpt_path)
model = model.eval().to('cuda')

for t in dataset.on_device_transform.transforms:
    if isinstance(t, NAGAddKeysTo):
        t.delete_after = False


for idx in range(len(dataset.all_base_cloud_ids[mode])):
    raw_ply_file_path = dataset.processed_to_raw_path(dataset.processed_paths[idx])
    filename = os.path.basename(raw_ply_file_path).split('.')[0]
    print('filename = ', filename)

    raw_data = read_ply(raw_ply_file_path)
    test_xyz = np.vstack((raw_data['x'], raw_data['y'], raw_data['z'])).T
    # gt_label = raw_data['semantic']
    gt_label = raw_data['label'].astype(np.uint8)
    gt_label = gt_label.reshape(-1, 1)
    print('gt_label range = ', np.unique(gt_label))
    gt_instance = raw_data['instance']

    xyz_list = []
    pred_label_list = []
    obj_index_list = []
    for tile_idx in range(len(dataset)):
        temp_file_name = osp.splitext(dataset.processed_paths[tile_idx])[0].split('/')[-1]
        temp_file_name = temp_file_name.split('__TILE_')[0]
        print('temp_file_name = ', temp_file_name)
        if temp_file_name == filename:
            print('loading file from = ', dataset.processed_paths[tile_idx])
            nag = dataset[tile_idx]
            nag = dataset.on_device_transform(nag.cuda())
            with torch.no_grad():
                output = model(nag)
            nag[0].semantic_pred = output.voxel_semantic_pred(super_index=nag[0].super_index)
            pred_label = nag[0].semantic_pred.cpu().numpy()
            pred_label = pred_label.reshape(-1, 1)

            pred_label = np.vectorize(ourkitti360_to_wuhan.get)(pred_label)
            # print('pred_label.shape = ', pred_label.shape)
            # print('pred_label range = ', np.unique(pred_label))
            # exit()

            xyz = nag[0].pos.cpu().numpy()
            xyz = xyz.reshape(np.size(xyz, 0), 3)

            if tasks == 'panoptic':
                vox_y, vox_index, vox_obj_pred = output.voxel_panoptic_pred(super_index=nag[0].super_index)
                nag[0].obj_pred = vox_obj_pred
                vox_index = vox_index.cpu().numpy()
                vox_index = vox_index.reshape(-1, 1)
                # thing_classes = dataset.thing_classes
                # vox_index_filtered = np.full_like(vox_index, -1)
                # for i in range(len(vox_index)):
                #     if vox_index[i] in thing_classes:
                #         vox_index_filtered[i] = vox_index[i]

            # collect all the tiles data
            xyz_list.append(xyz)
            pred_label_list.append(pred_label)
            obj_index_list.append(vox_index + 1000 * tile_idx)
        else:
            continue

    xyz = np.concatenate(xyz_list, axis=0)
    pred_label = np.concatenate(pred_label_list, axis=0)
    obj_index = np.concatenate(obj_index_list, axis=0)

    print('xyz.shape = ', xyz.shape)
    print('pred_label.shape = ', pred_label.shape)
    print('obj_index.shape = ', obj_index.shape)

    class_rgb = np.array([whu3d_colors[i] for i in pred_label])
    class_rgb = class_rgb.reshape(np.size(class_rgb, 0), 3)
    downsample_pred_file_path = save_dir + '/' + filename + '_downsample.ply'
    write_ply(downsample_pred_file_path, [xyz.astype(np.float64), class_rgb.astype(np.uint8), 
                                          pred_label.astype(np.uint8), obj_index.astype(np.int32)], 
                                         ['x', 'y', 'z', 'red', 'green', 'blue', 'pred_label', 'pred_instance'])


    neighbors = knn_2(torch.FloatTensor(xyz), torch.FloatTensor(test_xyz), 1, r_max=1)[0]

    pred_label_Tensor = torch.FloatTensor(pred_label)
    final_label = pred_label_Tensor[neighbors]
    final_label = final_label.numpy()
    final_label = final_label.reshape(np.size(final_label, 0), 1)
    final_label = final_label.astype(np.uint8)
    final_class_rgb = np.array([whu3d_colors[i] for i in final_label])
    final_class_rgb = final_class_rgb.reshape(np.size(final_class_rgb, 0), 3)

    obj_index_Tensor = torch.FloatTensor(obj_index)
    final_obj_index = obj_index_Tensor[neighbors]
    final_obj_index = final_obj_index.numpy()
    final_obj_index = final_obj_index.reshape(np.size(final_obj_index, 0), 1)

    gt_copy = gt_label.copy()
    error = np.where(gt_copy == final_label, 0, 1)

    print("final_label 取值范围是: [{}, {}]".format(np.min(final_label), np.max(final_label)))
    print("gt_label 取值范围是: [{}, {}]".format(np.min(gt_label), np.max(gt_label)))

    merge_pred_file_path = save_dir + '/' + filename + '.ply'
    write_ply(merge_pred_file_path, [test_xyz.astype(np.float64), final_class_rgb.astype(np.uint8), 
                                    gt_label.astype(np.uint8) ,final_label.astype(np.uint8), error.astype(np.uint8),
                                    final_obj_index.astype(np.int32)], 
                                    ['x', 'y', 'z', 'red', 'green', 'blue', 'gt_label', 'pred_label', 'error', 'instance'])

