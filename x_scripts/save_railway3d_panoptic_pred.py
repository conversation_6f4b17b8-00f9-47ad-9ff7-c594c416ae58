import os
import sys
sys.path.append('/home/<USER>/qbdata/repos/superpoint_transformer_v2')
import torch
import pyrootutils
pyrootutils.setup_root("/home/<USER>/qbdata/repos/superpoint_transformer_v2", pythonpath=True)

# `pyrootutils.setup_root(...)` is an optional line at the top of each entry file
# that helps to make the environment more robust and convenient
import numpy as np
from omegaconf import OmegaConf
OmegaConf.register_new_resolver("eval", eval)

import hydra
import torch
from src.utils import init_config
from src.transforms import *
from src.utils.widgets import *
from src.data import *
from src.datasets.railway3d_config import *
from src.utils.helper_ply import read_ply, write_ply
from src.utils.neighbors import knn_2

# Parse the configs using hydra
cfg = init_config(overrides=[
    f"experiment=panoptic/railway3d_11g",  # 设置实验的任务和具体实验
    # f"ckpt_path=/home/<USER>/qbdata/data/spt_logs/panoptic_spilt_8_with_intensity/panoptic_spilt_8_checkpoints/epoch_379.ckpt",  # 设置模型权重的路径
    f"ckpt_path=/home/<USER>/qbdata/data/spt_logs/panoptic_spilt_8_without_intensity/checkpoints/epoch_499.ckpt",  # 设置模型权重的路径
    f"datamodule.load_full_res_idx={True}"  # only when you need full-resolution predictions 
])
mode = 'test' # 'train', 'val', 'test'
tasks = 'panoptic'  # 'semantic', 'instance', 'panoptic'
# save_dir = '/home/<USER>/qbdata/data/spt_logs/panoptic_spilt_8_with_intensity/panoptic_predict'
save_dir = '/home/<USER>/qbdata/data/spt_logs/panoptic_spilt_8_without_intensity/panoptic_predict'
print('save_dir = ', save_dir)
if not os.path.exists(save_dir):
    os.makedirs(save_dir)


# Instantiate the datamodule
datamodule = hydra.utils.instantiate(cfg.datamodule)
datamodule.prepare_data()
datamodule.setup()

# Pick among train, val, and test datasets. It is important to note that
# the train dataset produces augmented spherical samples of large 
# scenes, while the val and test dataset

# dataset_name = datamodule.dataset_name

stage = 'train' if mode in ['train', 'val'] else mode
print('stage = ', stage)
if mode == 'train':
    dataset = datamodule.train_dataset
elif mode == 'val':
    dataset = datamodule.val_dataset
elif mode == 'test':
    dataset = datamodule.test_dataset
print('dataset = ', dataset)

# Load model with feature saving enabled
model = hydra.utils.instantiate(cfg.model)
model = model._load_from_checkpoint(cfg.ckpt_path)
model = model.eval().to('cuda')

for t in dataset.on_device_transform.transforms:
    if isinstance(t, NAGAddKeysTo):
        t.delete_after = False

test_set_path = dataset.raw_dir + '/' + stage
file_name_list = [file_name[:-4] for file_name in os.listdir(test_set_path) if file_name[-4:] == '.ply']
print('file_name_list : ', file_name_list)

for idx in range(len(file_name_list)):
    filename = file_name_list[idx]
    raw_ply_file_path = osp.join(test_set_path, filename + '.ply')
    print('processing : ', raw_ply_file_path)

    raw_data = read_ply(raw_ply_file_path)
    test_xyz = np.vstack((raw_data['x'], raw_data['y'], raw_data['z'])).T
    gt_label = raw_data['class']
    gt_label = gt_label.reshape(-1, 1)
    # print('gt_label range = ', np.unique(gt_label))
    gt_instance = raw_data['instance']

    xyz_list = []
    pred_label_list = []
    obj_index_list = []
    xyz_list = []
    pred_label_list = []
    obj_index_list = []
    for tile_idx in range(len(dataset)):
        temp_file_name = osp.splitext(dataset.processed_paths[tile_idx])[0].split('/')[-1]
        temp_file_name = temp_file_name.split('__TILE_')[0]
        # print('temp_file_name = ', temp_file_name)
        if temp_file_name == filename:
            print('loading file from = ', dataset.processed_paths[tile_idx])
            nag = dataset[tile_idx]
            nag = dataset.on_device_transform(nag.cuda())
            with torch.no_grad():
                output = model(nag)
            nag[0].semantic_pred = output.voxel_semantic_pred(super_index=nag[0].super_index)
            pred_label = nag[0].semantic_pred.cpu().numpy()
            pred_label = pred_label.reshape(-1, 1)

            xyz = nag[0].pos.cpu().numpy()
            xyz = xyz.reshape(np.size(xyz, 0), 3)

            if tasks == 'panoptic':
                vox_y, vox_index, vox_obj_pred = output.voxel_panoptic_pred(super_index=nag[0].super_index)
                nag[0].obj_pred = vox_obj_pred
                vox_index = vox_index.cpu().numpy()
                vox_index = vox_index.reshape(-1, 1)
                # thing_classes = dataset.thing_classes
                # vox_index_filtered = np.full_like(vox_index, -1)
                # for i in range(len(vox_index)):
                #     if vox_index[i] in thing_classes:
                #         vox_index_filtered[i] = vox_index[i]

            # collect all the tiles data
            xyz_list.append(xyz)
            pred_label_list.append(pred_label)
            obj_index_list.append(vox_index + 1000 * tile_idx)
        else:
            continue

    xyz = np.concatenate(xyz_list, axis=0)
    pred_label = np.concatenate(pred_label_list, axis=0)
    obj_index = np.concatenate(obj_index_list, axis=0)

    # print('xyz.shape = ', xyz.shape)
    # print('pred_label.shape = ', pred_label.shape)
    # print('obj_index.shape = ', obj_index.shape)

    # rgb = CLASS_COLORS[raw_semseg_y.squeeze()]
    class_rgb = CLASS_COLORS[pred_label.squeeze()]
    downsample_pred_file_path = save_dir + '/' + filename + '_downsample.ply'
    write_ply(downsample_pred_file_path, [xyz.astype(np.float64), class_rgb.astype(np.uint8), 
                                          pred_label.astype(np.uint8), obj_index.astype(np.int32)], 
                                         ['x', 'y', 'z', 'red', 'green', 'blue', 'pred_label', 'pred_instance'])


    neighbors = knn_2(torch.FloatTensor(xyz), torch.FloatTensor(test_xyz), 1, r_max=1)[0]

    pred_label_Tensor = torch.FloatTensor(pred_label)
    final_label = pred_label_Tensor[neighbors]
    final_label = final_label.numpy()
    final_label = final_label.reshape(np.size(final_label, 0), 1)
    final_label = final_label.astype(np.uint8)
    final_class_rgb = CLASS_COLORS[final_label.squeeze()]
    final_class_rgb = final_class_rgb.reshape(np.size(final_class_rgb, 0), 3)

    obj_index_Tensor = torch.FloatTensor(obj_index)
    final_obj_index = obj_index_Tensor[neighbors]
    final_obj_index = final_obj_index.numpy()
    final_obj_index = final_obj_index.reshape(np.size(final_obj_index, 0), 1)

    gt_copy = gt_label.copy()
    error = np.where(gt_copy == final_label, 0, 1)

    # print("final_label 取值范围是: [{}, {}]".format(np.min(final_label), np.max(final_label)))
    # print("gt_label 取值范围是: [{}, {}]".format(np.min(gt_label), np.max(gt_label)))

    merge_pred_file_path = save_dir + '/' + filename + '.ply'
    print('Saving file: ', merge_pred_file_path)
    write_ply(merge_pred_file_path, [test_xyz.astype(np.float64), final_class_rgb.astype(np.uint8), 
                                    gt_label.astype(np.uint8) ,final_label.astype(np.uint8), error.astype(np.uint8),
                                    final_obj_index.astype(np.int32)], 
                                    ['x', 'y', 'z', 'red', 'green', 'blue', 'gt_label', 'pred_label', 'error', 'instance'])
