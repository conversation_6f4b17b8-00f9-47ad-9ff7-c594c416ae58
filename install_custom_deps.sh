#!/bin/bash

# 安装自定义依赖项（从源码编译）
set -e

echo "=============================================="
echo "  🔧 安装自定义依赖项 🔧                    "
echo "=============================================="
echo

# 确保在项目根目录
cd "$(dirname "$0")"

echo "📁 创建依赖项目录"
mkdir -p src/dependencies

echo
echo "🚀 Step 1: 安装FRNN (Fast Range Nearest Neighbors)"
echo

if [ ! -d "src/dependencies/FRNN" ]; then
    echo "📥 克隆FRNN仓库..."
    git clone --recursive https://gitee.com/boqiu1314/FRNN.git src/dependencies/FRNN
else
    echo "✓ FRNN仓库已存在"
fi

cd src/dependencies/FRNN

echo "🔨 编译prefix_sum..."
cd external/prefix_sum
python setup.py install

echo "🔨 编译FRNN..."
cd ../../  # 回到FRNN目录
python setup.py install

echo "✅ FRNN安装完成"

# 回到项目根目录
cd ../../../

echo
echo "🚀 Step 2: 安装Point Geometric Features"
echo

if [ ! -d "src/dependencies/point_geometric_features" ]; then
    echo "📥 克隆Point Geometric Features仓库..."
    git clone https://gitee.com/boqiu1314/point_geometric_features.git src/dependencies/point_geometric_features
else
    echo "✓ Point Geometric Features仓库已存在"
fi

echo "🔨 安装Point Geometric Features..."
pip install src/dependencies/point_geometric_features
python -m pip install pgeof

echo "✅ Point Geometric Features安装完成"

echo
echo "🚀 Step 3: 安装Parallel Cut-Pursuit和Grid Graph"
echo

if [ ! -d "src/dependencies/parallel_cut_pursuit" ]; then
    echo "📥 克隆Parallel Cut-Pursuit仓库..."
    git clone https://gitee.com/boqiu1314/parallel-cut-pursuit.git src/dependencies/parallel_cut_pursuit
else
    echo "✓ Parallel Cut-Pursuit仓库已存在"
fi

if [ ! -d "src/dependencies/grid_graph" ]; then
    echo "📥 克隆Grid Graph仓库..."
    git clone https://gitee.com/boqiu1314/grid-graph.git src/dependencies/grid_graph
else
    echo "✓ Grid Graph仓库已存在"
fi

echo "🔨 编译Parallel Cut-Pursuit和Grid Graph..."
python x_scripts/setup_dependencies.py build_ext

echo "✅ 自定义依赖项安装完成"

echo
echo "🧪 最终测试..."
python -c "
try:
    import src
    print('✅ SuperCluster模块导入成功!')
    
    # 测试一些关键功能
    from src.data import Data
    from src.transforms import Transform
    print('✅ 核心数据结构导入成功!')
    
    print('🎉 SuperCluster安装完全成功!')
    print('现在可以运行: python src/train.py')
    
except ImportError as e:
    print(f'❌ 导入失败: {e}')
    print('可能需要检查自定义依赖项的编译')
"

echo
echo "🎯 安装完成！"
echo "建议运行测试确保一切正常:"
echo "  python -c 'import src; print(\"SuperCluster ready!\")'"
echo "  python src/train.py --help"
