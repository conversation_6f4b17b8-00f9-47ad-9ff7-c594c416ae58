# 可能有问题的依赖项 - 需要特殊处理或本地安装
# Potentially problematic dependencies - may need special handling

# 可视化相关 (可能因为镜像源问题)
plotly==5.9.0

# Jupyter生态系统 (可能版本冲突)
jupyterlab>=3
ipywidgets>=7.6
jupyter-dash
notebook>=5.3
ipykernel
ipyfilechooser

# PyTorch Geometric生态系统 (需要CUDA支持)
torchmetrics==0.11.4
torch_geometric==2.3.0
# 这些需要从特定URL安装:
# pip install pyg_lib torch_scatter torch_cluster -f https://data.pyg.org/whl/torch-2.1.2+cu121.html

# Hydra扩展 (可能依赖问题)
hydra-colorlog
hydra-submitit-launcher

# 专业工具 (可能需要特殊编译)
open3d
torch_tb_profiler
wandb

# 自定义依赖项 (需要从源码安装)
# FRNN - 需要从 git clone https://gitee.com/boqiu1314/FRNN.git 安装
# Point Geometric Features - 需要从源码安装
# Parallel Cut-Pursuit - 需要从源码安装
