# path to root directory
# this requires PROJECT_ROOT environment variable to exist
# PROJECT_ROOT is inferred and set by pyrootutils package in `train.py` and `eval.py`
root_dir: ${oc.env:PROJECT_ROOT}

# path to data directory
data_dir: /home/<USER>/qbdata/data/

# path to logging directory
log_dir: /home/<USER>/qbdata/data/spt_logs/panoptic_spilt_8_without_intensity/

# path to output directory, created dynamically by hydra
# path generation pattern is specified in `configs/hydra/default.yaml`
# use it to store all files generated during the run, like ckpts and metrics
output_dir: /home/<USER>/qbdata/data/spt_logs/panoptic_spilt_8_without_intensity/

# path to working directory
work_dir: ${hydra:runtime.cwd}
