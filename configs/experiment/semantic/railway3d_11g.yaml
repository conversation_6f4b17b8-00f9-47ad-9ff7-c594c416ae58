# @package _global_

# to execute this experiment run:
# python train.py experiment=semantic/railway3d_11g

# This configuration allows training SPT on a single 11G GPU, with a
# training procedure comparable with the default 
# experiment/semantic/railway3d configuration configuration.
# Among the multiple ways of reducing memory impact, we choose here to
#   - divide the dataset into smaller tiles (facilitates preprocessing
#     and inference on smaller GPUs)
#   - reduce the number of samples in each batch (facilitates training
#     on smaller GPUs)
# To keep the total number of training steps consistent with the default
# configuration, while keeping informative gradient despite the smaller
# batches, we use gradient accumulation and reduce the number of epochs.
# DISCLAIMER: the tiling procedure may increase the preprocessing time
# (more raw data reading steps), and slightly reduce mode performance
# (less diversity in the spherical samples)

defaults:
  - override /datamodule: semantic/railway3d.yaml
  - override /model: semantic/spt-2.yaml
  - override /trainer: gpu.yaml

# all parameters below will be merged with parameters from default configurations set above
# this allows you to overwrite only specified parameters

datamodule:
  pc_tiling: 1 # 3  # split each floor into 2^pc_tiling=8 tiles, based on their principal components. Reduces preprocessing- and inference-time GPU memory
  sample_graph_k: 2  # 2 spherical samples in each batch instead of 4. Reduces train-time GPU memory


callbacks:
  gradient_accumulator:
    scheduling:
      0:
        2  # accumulate gradient every 2 batches, to make up for reduced batch size

trainer:
  max_epochs: 500  # to keep same nb of steps: 8x more tiles, 2-step gradient accumulation -> epochs/4

model:
  optimizer:
    lr: 0.01
    weight_decay: 1e-4
    # lr: 0.1
    # weight_decay: 1e-2

logger:
  wandb:
    project: "spt_railway3d"
    name: "SPT-64"
