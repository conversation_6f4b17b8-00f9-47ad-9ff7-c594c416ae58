# Logging

## Structure of `logs/` directory
Logs directory structure.

Your logs will be saved under the following structure:

```
└── logs
    ├── {{train, eval}}               # Task name
    │   ├── runs                        # Logs generated by single runs
    │   │   ├── YYYY-MM-DD_HH-MM-SS       # Datetime of the run
    │   │   │   ├── .hydra                  # Hydra logs
    │   │   │   ├── csv                     # Csv logs
    │   │   │   ├── wandb                   # Weights&Biases logs
    │   │   │   ├── checkpoints             # Training checkpoints
    │   │   │   └── ...                     # Any other thing saved during training
    │   │   └── ...
    │   │
    │   └── multiruns                   # Logs generated by multiruns (ie using --multirun)
    │       ├── YYYY-MM-DD_HH-MM-SS       # Datetime of the multirun
    │       │   ├──1                        # Multirun job number
    │       │   ├──2
    │       │   └── ...
    │       └── ...
    │
    └── debug                           # Logs generated when debugging config is attached
        └── ...

```

## Setting up your own `data/` and `logs/` paths
The `data/` and `logs/` directories will store all your datasets and training 
logs. By default, these are placed in the repository directory. 

Since this may take some space, or your heavy data may be stored elsewhere, you 
may specify other paths for these directories by creating a 
`configs/local/defaults.yaml` file containing the following:

```yaml
# @package paths

# path to data directory
data_dir: /path/to/your/data/

# path to logging directory
log_dir: /path/to/your/logs/
```

## Logger options
By default, your logs will automatically be uploaded to 
[Weights and Biases](https://wandb.ai), from where you can track and compare 
your experiments.

Other loggers are available in `configs/logger/`. See 
[Lightning-Hydra](https://github.com/ashleve/lightning-hydra-template) for more
information.
