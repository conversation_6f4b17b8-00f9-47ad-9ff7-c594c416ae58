#!/bin/bash

# 修复依赖冲突
set -e

echo "=============================================="
echo "  🔧 修复依赖冲突 🔧                        "
echo "=============================================="
echo

echo "📋 检查当前环境中的冲突包..."
echo

# 检查已安装的包
python -c "
import pkg_resources
import sys

problematic_packages = ['dash', 'lyft-dataset-sdk', 'mmdet3d', 'open3d']
installed_packages = [pkg.project_name for pkg in pkg_resources.working_set]

print('已安装的问题包:')
for pkg in problematic_packages:
    if pkg in installed_packages:
        version = pkg_resources.get_distribution(pkg).version
        print(f'  ✓ {pkg} {version}')
    else:
        print(f'  ✗ {pkg} (未安装)')
"

echo
echo "🔧 Step 1: 安装缺失的依赖项"
echo

# 安装dash的依赖项
echo "修复dash依赖..."
pip install ansi2html dash-core-components==2.0.0 dash-html-components==2.0.0 dash-table==5.0.0 nest-asyncio retrying

# 安装lyft-dataset-sdk的依赖项
echo "修复lyft-dataset-sdk依赖..."
pip install black fire flake8 pytest

# 安装mmdet3d的依赖项
echo "修复mmdet3d依赖..."
pip install scikit-image tensorboard

# 安装open3d的依赖项
echo "修复open3d依赖..."
pip install configargparse

# 处理nbformat版本冲突
echo "修复nbformat版本冲突..."
pip install nbformat==5.7.0 --force-reinstall

echo "✅ 依赖项修复完成"

echo
echo "🔧 Step 2: 重新安装可能有问题的包"
echo

# 重新安装open3d以确保兼容性
pip install open3d --force-reinstall

echo "✅ 包重新安装完成"

echo
echo "🧪 Step 3: 验证修复结果"
echo

python -c "
import sys
try:
    # 测试主要包的导入
    import dash
    print('✅ dash导入成功')
    
    import open3d
    print('✅ open3d导入成功')
    
    # 测试其他关键包
    import torch
    import pytorch_lightning
    import plotly
    import hydra
    
    print('✅ 所有主要包导入成功!')
    
except ImportError as e:
    print(f'❌ 导入失败: {e}')
    sys.exit(1)
"

echo
echo "🎯 依赖冲突修复完成！"
echo "现在可以继续安装剩余的SuperCluster依赖项"
