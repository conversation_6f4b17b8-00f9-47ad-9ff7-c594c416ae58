#!/bin/bash

# 安装剩余依赖项
set -e

echo "=============================================="
echo "  🚀 安装剩余依赖项 🚀                      "
echo "=============================================="
echo

echo "📦 Step 1: 安装可视化相关包"
echo

# 可视化包
pip install plotly==5.9.0
pip install matplotlib  # 确保最新版本

echo "✅ 可视化包安装完成"

echo
echo "📦 Step 2: 安装Jupyter生态系统"
echo

# Jupyter相关
pip install "jupyterlab>=3" "ipywidgets>=7.6" jupyter-dash
pip install "notebook>=5.3" "ipywidgets>=7.5"
pip install ipykernel
pip install ipyfilechooser

echo "✅ Jupyter生态系统安装完成"

echo
echo "📦 Step 3: 安装PyTorch Geometric扩展"
echo

# PyTorch Geometric扩展 - 使用正确的CUDA版本
pip install torchmetrics==0.11.4
pip install torch_geometric==2.3.0

# PyG扩展包 - 从官方源安装
pip install pyg_lib torch_scatter torch_cluster -f https://data.pyg.org/whl/torch-2.1.2+cu121.html

echo "✅ PyTorch Geometric扩展安装完成"

echo
echo "📦 Step 4: 安装Hydra扩展"
echo

pip install hydra-colorlog
pip install hydra-submitit-launcher

echo "✅ Hydra扩展安装完成"

echo
echo "📦 Step 5: 安装专业工具"
echo

# 3D处理
pip install open3d

# 实验跟踪和监控
pip install wandb
pip install torch_tb_profiler

echo "✅ 专业工具安装完成"

echo
echo "🧪 测试所有导入..."
python -c "
import sys
packages_to_test = [
    ('torch', 'PyTorch'),
    ('torch_geometric', 'PyTorch Geometric'),
    ('pytorch_lightning', 'PyTorch Lightning'),
    ('hydra', 'Hydra'),
    ('plotly', 'Plotly'),
    ('open3d', 'Open3D'),
    ('wandb', 'Weights & Biases'),
    ('pandas', 'Pandas'),
    ('numpy', 'NumPy'),
    ('matplotlib', 'Matplotlib'),
    ('seaborn', 'Seaborn'),
    ('h5py', 'HDF5'),
    ('plyfile', 'PLY File'),
    ('colorhash', 'ColorHash'),
    ('numba', 'Numba'),
    ('omegaconf', 'OmegaConf'),
    ('pyrootutils', 'PyRootUtils'),
    ('rich', 'Rich'),
    ('tqdm', 'TQDM'),
    ('gdown', 'GDown')
]

failed_imports = []
for package, name in packages_to_test:
    try:
        __import__(package)
        print(f'✅ {name}')
    except ImportError as e:
        print(f'❌ {name}: {e}')
        failed_imports.append(name)

if failed_imports:
    print(f'\n⚠️  以下包导入失败: {failed_imports}')
    sys.exit(1)
else:
    print('\n🎉 所有主要包导入成功!')
"

echo
echo "🎯 接下来安装自定义依赖项..."
echo "这些需要从源码编译，可能需要几分钟时间"
echo
