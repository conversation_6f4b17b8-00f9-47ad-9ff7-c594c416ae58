#!/bin/bash

# Quick fix for pip configuration and basic installation
set -e

echo "🔧 Fixing pip configuration..."

# Reset pip configuration to use standard PyPI
pip config unset global.index-url 2>/dev/null || true
pip config unset global.trusted-host 2>/dev/null || true
pip config unset global.extra-index-url 2>/dev/null || true

# Set to use standard PyPI
pip config set global.index-url https://pypi.org/simple/
pip config set global.trusted-host pypi.org

echo "✓ Pip configuration fixed"

# Test pip with a simple package
echo "🧪 Testing pip installation..."
pip install --upgrade pip

echo "📦 Installing basic dependencies..."

# Install core packages one by one to identify issues
pip install torchmetrics
pip install pytorch-lightning
pip install hydra-core
pip install omegaconf
pip install pyrootutils

echo "✓ Core packages installed successfully"

echo "📦 Installing PyTorch Geometric..."
pip install torch-geometric

echo "📦 Installing PyG extensions..."
pip install pyg_lib torch_scatter torch_cluster -f https://data.pyg.org/whl/torch-2.1.0+cu121.html

echo "✅ Basic installation completed!"
echo "Now you can run: pip install -r requirements.txt"
