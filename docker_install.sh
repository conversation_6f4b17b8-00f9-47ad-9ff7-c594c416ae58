#!/bin/bash

# Docker Installation Script for SuperCluster
# Compatible with Python 3.10.13, PyTorch 2.1.2, CUDA 12.1

set -e  # Exit on any error

echo "=============================================="
echo "  🧩 SuperCluster Docker Installation 🤖    "
echo "=============================================="
echo

# Check Python version
PYTHON_VERSION=$(python --version 2>&1 | awk '{print $2}')
echo "✓ Python version: $PYTHON_VERSION"

# Check PyTorch version
TORCH_VERSION=$(python -c "import torch; print(torch.__version__)" 2>/dev/null || echo "Not found")
echo "✓ PyTorch version: $TORCH_VERSION"

# Check CUDA version
CUDA_VERSION=$(python -c "import torch; print(torch.version.cuda)" 2>/dev/null || echo "Not found")
echo "✓ CUDA version: $CUDA_VERSION"

echo
echo "⭐ Installing pip dependencies from requirements.txt"
echo

# Upgrade pip first
pip install --upgrade pip

# Install main requirements
pip install -r requirements.txt

echo
echo "⭐ Installing FRNN (Fast Range Nearest Neighbors)"
echo

# Create dependencies directory if it doesn't exist
mkdir -p src/dependencies

# Clone and install FRNN
if [ ! -d "src/dependencies/FRNN" ]; then
    git clone --recursive https://gitee.com/boqiu1314/FRNN.git src/dependencies/FRNN
fi

cd src/dependencies/FRNN

# Install prefix_sum routine first
cd external/prefix_sum
python setup.py install

# Install FRNN
cd ../../  # back to FRNN directory
python setup.py install

# Return to project root
cd ../../../

echo
echo "⭐ Installing Point Geometric Features"
echo

# Clone and install point geometric features
if [ ! -d "src/dependencies/point_geometric_features" ]; then
    git clone https://gitee.com/boqiu1314/point_geometric_features.git src/dependencies/point_geometric_features
fi

pip install src/dependencies/point_geometric_features
python -m pip install pgeof

echo
echo "⭐ Installing Parallel Cut-Pursuit and Grid Graph"
echo

# Clone repositories
if [ ! -d "src/dependencies/parallel_cut_pursuit" ]; then
    git clone https://gitee.com/boqiu1314/parallel-cut-pursuit.git src/dependencies/parallel_cut_pursuit
fi

if [ ! -d "src/dependencies/grid_graph" ]; then
    git clone https://gitee.com/boqiu1314/grid-graph.git src/dependencies/grid_graph
fi

# Compile the projects using the setup script
python x_scripts/setup_dependencies.py build_ext

echo
echo "⭐ Verifying installation"
echo

# Test imports
python -c "
import torch
import torch_geometric
import pytorch_lightning as pl
import hydra
import plotly
import open3d
import h5py
import plyfile
import colorhash
import seaborn
import numba
import wandb
print('✓ All main packages imported successfully')
"

echo
echo "🚀 SuperCluster installation completed successfully!"
echo
echo "Next steps:"
echo "1. Verify your installation by running: python -c 'import src; print(\"SuperCluster ready!\")'"
echo "2. Run tests if available: python -m pytest tests/"
echo "3. Start training: python src/train.py"
echo
